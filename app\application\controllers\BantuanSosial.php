<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Penerimaan_BLT_Model $penerimaan_blt
 * @property Detail_Penerima_BLT_Model $detail_penerima_blt
 * @property Warga_Model $warga
 * @property Setting_Umum $settingumum
 * @property Kontak_Penting $kontakpenting
 */
class BantuanSosial extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Penerimaan_BLT_Model', 'penerimaan_blt');
        $this->load->model('Detail_Penerima_BLT_Model', 'detail_penerima_blt');
        $this->load->model('Warga_Model', 'warga');
        $this->load->model('Setting_Umum', 'settingumum');
        $this->load->model('Kontak_Penting', 'kontakpenting');
    }

    public function index()
    {
        $data = array();

        $where = array(
            'a.id_user' => $this->subdomain_account->id
        );

        $setting = $this->settingumum->getDefaultData($where);
        $data['title'] = 'Bantuan Sosial';
        $data['setting'] = $setting->row();
        $data['kontakpenting'] = $this->kontakpenting->getDefaultData($where)->result();
        $data['content'] = 'bantuan_sosial/index';

        // Ambil data penerimaan BLT yang sudah ada
        $data['penerimaan_blt'] = $this->penerimaan_blt->select('a.*, COUNT(b.id) as total_penerima, 
            SUM(CASE WHEN b.status_verifikasi = "terverifikasi" THEN 1 ELSE 0 END) as terverifikasi,
            SUM(CASE WHEN b.status_verifikasi = "menunggu_verifikasi" THEN 1 ELSE 0 END) as menunggu_verifikasi')
            ->join('detail_penerima_blt b', 'b.id_penerimaan_blt = a.id', 'LEFT')
            ->group_by('a.id')
            ->order_by('a.tanggal_dibuat', 'DESC')
            ->getDefaultData($where)
            ->result();

        if ($setting->num_rows() > 0) {
            // ProGides platform always uses ProGides theme regardless of theme setting
            if (getPlatformName() == 'ProGides') {
                $data['content'] = 'progides/bantuan_sosial';
                return $this->load->view('progides/master', $data);
            } elseif ($this->subdomain_account->themeid == 2) {
                $data['content'] = 'profile/bantuan_sosial';
                return $this->load->view('profile/master', $data);
            } else {
                return $this->load->view('landing/master', $data);
            }
        } else {
            return $this->load->view('profile_v2/configuration', $data);
        }
    }

    public function detail($id)
    {
        $data = array();

        $where = array(
            'a.id_user' => $this->subdomain_account->id
        );

        $setting = $this->settingumum->getDefaultData($where);
        $data['setting'] = $setting->row();
        $data['kontakpenting'] = $this->kontakpenting->getDefaultData($where)->result();

        // Ambil data penerimaan BLT berdasarkan ID
        $penerimaan_blt = $this->penerimaan_blt->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => $this->subdomain_account->id
        ));

        if ($penerimaan_blt->num_rows() == 0) {
            return redirect('bantuan-sosial');
        }

        $data['penerimaan_blt'] = $penerimaan_blt->row();
        $data['title'] = 'Detail ' . $data['penerimaan_blt']->deskripsi;

        // Ambil semua data penerima (terverifikasi dan belum)
        $data['penerima'] = $this->detail_penerima_blt->select('a.*, b.nama, b.alamat, b.rt, b.rw')
            ->join('warga b', 'b.nik = a.nik')
            ->get(array(
                'a.id_penerimaan_blt' => $id
            ))
            ->result();

        // Hitung ringkasan
        $total_penerima = count($data['penerima']);
        $total_terverifikasi = 0;
        foreach ($data['penerima'] as $row) {
            if ($row->status_verifikasi === 'terverifikasi') {
                $total_terverifikasi++;
            }
        }
        $total_belum_terverifikasi = $total_penerima - $total_terverifikasi;
        $nominal = (int) $data['penerimaan_blt']->nominal;

        $data['summary'] = array(
            'total_terverifikasi' => $total_terverifikasi,
            'total_dana_tersalurkan' => $nominal * $total_terverifikasi,
            'total_dana_belum_tersalurkan' => $nominal * $total_belum_terverifikasi,
            'total_dana_bantuan' => $nominal * $total_penerima,
            'nominal_per_penerima' => $nominal,
            'total_penerima' => $total_penerima
        );

        $data['content'] = 'bantuan_sosial/detail';

        if ($setting->num_rows() > 0) {
            // ProGides platform always uses ProGides theme regardless of theme setting
            if (getPlatformName() == 'ProGides') {
                $data['content'] = 'progides/bantuan_sosial_detail';
                return $this->load->view('progides/master', $data);
            } elseif ($this->subdomain_account->themeid == 2) {
                $data['content'] = 'profile/bantuan_sosial_detail';
                return $this->load->view('profile/master', $data);
            } else {
                return $this->load->view('landing/master', $data);
            }
        } else {
            return $this->load->view('profile_v2/configuration', $data);
        }
    }
}
