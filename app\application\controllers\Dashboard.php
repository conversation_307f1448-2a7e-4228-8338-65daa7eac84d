<?php

use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Setting_Umum $settingumum
 * @property Berita_Model $berita
 * @property Produk_Model $produk
 * @property Wisata_Model $wisata
 * @property TrafficReport $trafficreport
 * @property Master_Users $masterusers
 * @property Infografis_Model $infografis
 * @property Struktur_Organisasi $strukturorganisasi
 * @property CI_DB_query_builder $db
 * @property Arsip_Pengaduan $arsippengaduan
 * @property Bidangpengaduans $bidangpengaduans
 * @property Datatables $datatables
 * @property LogStrukturOrganisasi $logstrukturorganisasi
 * @property Slider_Popup $sliderpopup
 */
class Dashboard extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Setting_Umum', 'settingumum');
        $this->load->model('Berita_Model', 'berita');
        $this->load->model('Produk_Model', 'produk');
        $this->load->model('Wisata_Model', 'wisata');
        $this->load->model('TrafficReport', 'trafficreport');
        $this->load->model('Master_Users', 'masterusers');
        $this->load->model('Infografis_Model', 'infografis');
        $this->load->model('Struktur_Organisasi', 'strukturorganisasi');
        $this->load->model('Arsip_Pengaduan', 'arsippengaduan');
        $this->load->model('Bidangpengaduans', 'bidangpengaduans');
        $this->load->model('LogStrukturOrganisasi', 'logstrukturorganisasi');
        $this->load->model('Slider_Popup', 'sliderpopup');
    }

    public function index()
    {
        $mark = getGet('mark');
        $kabupatenget = getGet('kabupaten');
        $kecamatanget = getGet('kecamatan');
        $desa = getGet('desa');
        $currentuser = getCurrentUser();

        // Handle error messages from URL parameters
        $error = getGet('error');
        $error_file = getGet('file');
        $error_details = getGet('details');

        if (!isLogin()) {
            return redirect('auth/login');
        }

        if (isAdmin() || isOperatorDesa()) {
            $setting = $this->settingumum->get(array(
                'id_user' => getCurrentIdUser()
            ));

            if ($setting->num_rows() == 0) {
                return redirect('settingumum');
            }
        }

        if ($mark) {
            setSessionValue(array(
                'MARK' => $mark
            ));
        }

        $where = array();
        $where_in = array();

        $where_desa = array('role' => 'admin');
        $where_desa_in = array();

        $where_traffic = array();
        $where_traffic_in = array();
        $where_traffic_in2 = array();

        $userid = array();

        $data = array();
        if ((isPMD() || isKecamatan()) && !isOperatorDesa()) {
            $user = $this->masterusers->getDefaultData(array('id' => getCurrentIdUser()))->row();

            $kecamatan = $user->kecamatanid;

            if ($kecamatan == null) {
                $kelurahan = $this->db->get_where('kelurahan', array('id_kabkota' => $user->kabkotaid))->result();

                $data['kecamatan'] = $this->db->where_in('id_kabkota', $user->kabkotaid)
                    ->get_where('kecamatan')
                    ->result();
            } else {
                $kecamatan = explode(',', $kecamatan);
                $kelurahan = $this->db->where_in('id_kecamatan', $kecamatan)
                    ->get_where('kelurahan', array(
                        'id_kabkota' => $user->kabkotaid
                    ))->result();

                $data['kecamatan'] = $this->db->where_in('id_kecamatan', $kecamatan)
                    ->get_where('kecamatan')
                    ->result();
            }

            $kelurahanid = array();
            foreach ($kelurahan as $key => $value) {
                $kelurahanid[] = $value->id_kelurahan;
            }

            $users = $this->db->where_in('kelurahanid', $kelurahanid)->get('msusers')->result();

            foreach ($users as $key => $value) {
                $userid[] = $value->id;
            }

            if (count($userid) > 0) {
                $where_in['a.id_user'] = $userid;
                $where_desa_in['a.id'] = $userid;
                $where_traffic_in['b.userid'] = $userid;
                $where_traffic_in2['a.id'] = $userid;
            } else {
                $where['(1=0) ='] = true;
                $where_desa['(1=0) ='] = true;
                $where_traffic['(1=0) ='] = true;
            }
        }

        if ($desa != null) {
            if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                $where['id_user'] = getCurrentIdUser();
            } else {
                $where['id_user'] = $desa;
            }
        } else {
            if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                $where['id_user'] = getCurrentIdUser();
            }
        }

        if ($desa != null) {
            if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                $where_desa['id'] = getCurrentIdUser();
            } else {
                $where_desa['id'] = $desa;
            }
        } else {
            if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                $where_desa['id'] = getCurrentIdUser();
            }
        }

        if (isSuperAdmin() && !isAllPlatform()) {
            $where_desa['a.platformname'] = getCurrentPlatformName();
        }

        $berita = $where;
        if (!isAdmin()) {
            $berita['a.isverified'] = 1;

            if ($kabupatenget != null) {
                $berita['c.id_kabkota'] = $kabupatenget;
            }

            if ($kecamatanget != null) {
                $berita['c.id_kecamatan'] = $kecamatanget;
            }

            if (isSuperAdmin() && !isAllPlatform()) {
                $berita['b.platformname'] = getCurrentPlatformName();
            }
        }

        $produk = $where;
        if ($kabupatenget != null) {
            $produk['c.id_kabkota'] = $kabupatenget;
        }

        if ($kecamatanget != null) {
            $produk['c.id_kecamatan'] = $kecamatanget;
        }

        if (isSuperAdmin() && !isAllPlatform()) {
            $produk['b.platformname'] = getCurrentPlatformName();
        }

        $totaldesa = $where_desa;

        if ($kabupatenget != null) {
            $totaldesa['b.id_kabkota'] = $kabupatenget;
        }

        if ($kecamatanget != null) {
            $totaldesa['b.id_kecamatan'] = $kecamatanget;
        }

        if (isSuperAdmin() && !isAllPlatform()) {
            $totaldesa['a.platformname'] = getCurrentPlatformName();
        }

        $data['title'] = 'Dashboard';
        if (!isBPD()) {
            $data['content'] = 'dashboard_v2';
        } else {
            $data['content'] = 'dashboard_bpd';

            $data['today_report'] = $this->arsippengaduan->join('bidangpengaduan b', 'a.bidangpengaduanid = b.id')
                ->join('warga c', 'a.wargaid = c.nik')
                ->total(array(
                    'DATE(a.tanggalpengaduan) =' => getCurrentDate('Y-m-d'),
                    'a.userid' => $currentuser->userid
                ));
            $data['month_report'] = $this->arsippengaduan->join('bidangpengaduan b', 'a.bidangpengaduanid = b.id')
                ->join('warga c', 'a.wargaid = c.nik')
                ->total(array(
                    'MONTH(a.tanggalpengaduan) =' => getCurrentDate('m'),
                    'YEAR(a.tanggalpengaduan) =' => getCurrentDate('Y'),
                    'a.userid' => $currentuser->userid
                ));
            $data['year_report'] = $this->arsippengaduan->join('bidangpengaduan b', 'a.bidangpengaduanid = b.id')
                ->join('warga c', 'a.wargaid = c.nik')
                ->total(array(
                    'YEAR(a.tanggalpengaduan) =' => getCurrentDate('Y'),
                    'a.userid' => $currentuser->userid
                ));
            $data['total_report'] = $this->arsippengaduan->join('bidangpengaduan b', 'a.bidangpengaduanid = b.id')
                ->join('warga c', 'a.wargaid = c.nik')
                ->total(array(
                    'a.userid' => $currentuser->userid
                ));

            $data['bidangpengaduan'] = $this->bidangpengaduans->result(array(
                'userid' => $currentuser->userid
            ));
        }
        $data['berita'] = $this->berita->join('msusers b', 'b.id = a.id_user')
            ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid')
            ->total($berita, $where_in);
        $data['produk'] = $this->produk->join('msusers b', 'b.id = a.id_user')
            ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid')
            ->total($produk, $where_in);
        $data['totaldesa'] = $this->masterusers->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid', 'LEFT')
            ->total($totaldesa, $where_desa_in);
        $data['selecteddesa'] = $desa;

        if (!isAdmin()) {
            $where_performance = "";

            if ($kabupatenget != null) {
                $where_performance .= " AND b.id_kabkota = $kabupatenget";
            } else {
                if (isPMD()) {
                    $where_performance .= " AND b.id_kabkota = " . $user->kabkotaid;
                }
            }

            if ($kecamatanget != null) {
                $where_performance .= " AND b.id_kecamatan = $kecamatanget";
            } else {
                if (isPMD() || isKecamatan()) {
                    $performance_subdistricts = array();
                    foreach ($data['kecamatan'] as $key => $value) {
                        $performance_subdistricts[] = $value->id_kecamatan;
                    }

                    if (count($performance_subdistricts) > 0) {
                        $where_performance .= " AND b.id_kecamatan IN (" . implode(',', $performance_subdistricts) . ")";
                    }
                }
            }

            if (isSuperAdmin() && !isAllPlatform()) {
                $where_performance .= " AND a.platformname = '" . getCurrentPlatformName() . "'";
            }

            $data['performance'] = $this->db->query("SELECT a.id, a.platformname, b.nama_kelurahan, a.subdomain, c.total AS setting_umum, d.total AS kontak_penting, e.total AS infografis, f.total AS apbdesa, g.total AS warga, h.total AS strukturorganisasi, i.total AS berita, j.total AS produk, k.total AS produkhukum, l.total AS wisata, m.total AS slider, n.total AS highlight, o.total AS asset, p.total AS pengunjungberita FROM msusers a JOIN kelurahan b ON b.id_kelurahan = a.kelurahanid LEFT JOIN ( SELECT id_user, count(*) AS total FROM setting_umum GROUP BY id_user ) c ON c.id_user = a.id LEFT JOIN ( SELECT id_user, count(*) AS total FROM kontak_penting GROUP BY id_user ) d ON d.id_user = a.id LEFT JOIN ( SELECT id_user, count(*) AS total FROM infografis GROUP BY id_user ) e ON e.id_user = a.id LEFT JOIN ( SELECT id_user, count(*) AS total FROM apbdesa WHERE budget_total > 0 GROUP BY id_user ) f ON f.id_user = a.id LEFT JOIN ( SELECT id_user, COUNT(*) AS total FROM warga GROUP BY id_user ) g ON g.id_user = a.id LEFT JOIN ( SELECT userid, count(*) AS total FROM strukturorganisasi GROUP BY userid ) h ON h.userid = a.id LEFT JOIN ( SELECT id_user, count(*) AS total FROM berita GROUP BY id_user ) i ON i.id_user = a.id LEFT JOIN ( SELECT id_user, count(*) AS total FROM produk GROUP BY id_user ) j ON j.id_user = a.id LEFT JOIN ( SELECT userid, count(*) AS total FROM produkhukum GROUP BY userid ) k ON k.userid = a.id LEFT JOIN ( SELECT id_user, count(*) AS total FROM wisata GROUP BY id_user ) l ON l.id_user = a.id LEFT JOIN ( SELECT id_user, count(*) AS total FROM setting_slider GROUP BY id_user ) m ON m.id_user = a.id LEFT JOIN ( SELECT id_user, count(*) AS total FROM highlight GROUP BY id_user ) n ON n.id_user = a.id LEFT JOIN ( SELECT id_user, count(*) AS total FROM msasset GROUP BY id_user ) o ON o.id_user = a.id LEFT JOIN (select b.id_user, count(*) as total from visitorberita a join berita b on b.id = a.beritaid group by b.id_user) p ON p.id_user = a.id WHERE a.role = 'admin' $where_performance")->result();
        }
        $data['selectedkecamatan'] = $kecamatanget;

        $kabupatenResult = $this->masterusers->select('d.id_kabkota, d.nama_kabkota')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
            ->join('kecamatan c', 'c.id_kecamatan = b.id_kecamatan')
            ->join('kabkota d', 'd.id_kabkota = c.id_kabkota')
            ->where_in($where_desa_in)
            ->where($where_desa)
            ->order_by('d.nama_kabkota')
            ->group_by('d.id_kabkota, d.nama_kabkota')
            ->result();

        $data['kabupatendesa'] = $kabupatenResult;
        $data['selectedkabupaten'] = getGet('kabupaten');

        $where_traffic = array(
            'DATE(b.tanggal)' => getCurrentDate('Y-m-d'),
        );

        if ($desa != null) {
            if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                $where_traffic['b.userid'] = getCurrentIdUser();
            } else {
                $where_traffic['b.userid'] = $desa;
            }
        } else {
            if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                $where_traffic['b.userid'] = getCurrentIdUser();
            }
        }

        $data['visit_today'] = $this->trafficreport->getCurrent($where_traffic, $where_traffic_in);

        $where_traffic = array(
            'MONTH(b.tanggal)' => getCurrentDate('m'),
            'YEAR(b.tanggal)' => getCurrentDate('Y')
        );

        if ($desa != null) {
            if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                $where_traffic['b.userid'] = getCurrentIdUser();
            } else {
                $where_traffic['b.userid'] = $desa;
            }
        } else {
            if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                $where_traffic['b.userid'] = getCurrentIdUser();
            }
        }
        $data['visit_month'] = $this->trafficreport->getMonth($where_traffic, $where_traffic_in);

        $where_traffic = array(
            'YEAR(b.tanggal)' => getCurrentDate('Y')
        );

        if ($desa != null) {
            if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                $where_traffic['b.userid'] = getCurrentIdUser();
            } else {
                $where_traffic['b.userid'] = $desa;
            }
        } else {
            if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                $where_traffic['b.userid'] = getCurrentIdUser();
            }
        }

        $data['visit_year'] = $this->trafficreport->getYear($where_traffic, $where_traffic_in);

        $where_traffic = array();
        if ($desa != null) {
            if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                $where_traffic['b.userid'] = getCurrentIdUser();
            } else {
                $where_traffic['b.userid'] = $desa;
            }
        } else {
            if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                $where_traffic['b.userid'] = getCurrentIdUser();
            }
        }

        if ($kabupatenget != null) {
            $where_traffic['c.id_kabkota'] = $kabupatenget;
        }

        if ($kecamatanget != null) {
            $where_traffic['c.id_kecamatan'] = $kecamatanget;
        }

        if (isSuperAdmin() && !isAllPlatform()) {
            $where_traffic['a.platformname'] = getCurrentPlatformName();
        }

        $data['rank_month'] = $this->trafficreport->getRankMonth($where_traffic, $where_traffic_in2);
        $data['trafficreport'] = $this->trafficreport;

        $settingumum = $this->settingumum->getDefaultData($where);

        $terisi = 0;
        if ($settingumum->num_rows() > 0) {
            $settingumumRow = $settingumum->row();

            if ($settingumumRow->nama != null) {
                $terisi++;
            }

            if ($settingumumRow->foto != null) {
                $terisi++;
            }

            if ($settingumumRow->isi != null) {
                $terisi++;
            }

            if ($settingumumRow->jabatan != null) {
                $terisi++;
            }

            if ($settingumumRow->alamat != null) {
                $terisi++;
            }

            if ($settingumumRow->desa != null) {
                $terisi++;
            }

            if ($settingumumRow->email != null) {
                $terisi++;
            }

            if ($settingumumRow->jam_kerja != null) {
                $terisi++;
            }

            if ($settingumumRow->kontak != null) {
                $terisi++;
            }

            if ($settingumumRow->kecamatan != null) {
                $terisi++;
            }

            if ($settingumumRow->kabupaten != null) {
                $terisi++;
            }

            if ($settingumumRow->provinsi != null) {
                $terisi++;
            }

            if ($settingumumRow->kodepos != null) {
                $terisi++;
            }

            if ($settingumumRow->logo_desa != null) {
                $terisi++;
            }

            if ($settingumumRow->luas_tanah_kas != null) {
                $terisi++;
            }

            if ($settingumumRow->luas_tanah_desa != null) {
                $terisi++;
            }

            if ($settingumumRow->luas_dhkp != null) {
                $terisi++;
            }
        }

        $wisata = $where;

        if ($kabupatenget != null) {
            $wisata['c.id_kabkota'] = $kabupatenget;
        }

        if ($kecamatanget != null) {
            $wisata['c.id_kecamatan'] = $kecamatanget;
        }

        if (isSuperAdmin() && !isAllPlatform()) {
            $wisata['b.platformname'] = getCurrentPlatformName();
        }

        $data['persentase'] = round(($terisi / 17) * 100);
        $data['terisi'] = $terisi;
        $data['wisata'] = $this->wisata->join('msusers b', 'b.id = a.id_user')
            ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid')
            ->total($wisata, $where_in);

        $daily = array();
        for ($i = 0; $i < getCurrentDate('t'); $i++) {
            $where_traffic = array(
                'DATE(b.tanggal)' => getCurrentDate('Y-m') . '-' . ($i + 1)
            );

            if ($desa != null) {
                if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                    $where_traffic['b.userid'] = getCurrentIdUser();
                } else {
                    $where_traffic['b.userid'] = $desa;
                }
            } else {
                if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                    $where_traffic['b.userid'] = getCurrentIdUser();
                }
            }

            if (isPMD()) {
                if (count($where_traffic_in) == 0) {
                    $where_traffic['(1=0) ='] = true;
                }
            }

            $harian = $this->trafficreport->getCurrent($where_traffic, $where_traffic_in);

            $obj = new stdClass();
            $obj->y = $i + 1;
            $obj->a = $harian != null ? $harian : 0;

            $daily[$i] = $obj;
        }

        $data['report_daily'] = json_encode($daily);

        $monthly = array();
        for ($i = 0; $i < 12; $i++) {
            $where_traffic = array(
                'MONTH(b.tanggal)' => ($i + 1),
                'YEAR(b.tanggal)' => getCurrentDate('Y'),
            );

            if ($desa != null) {
                if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                    $where_traffic['b.userid'] = getCurrentIdUser();
                } else {
                    $where_traffic['b.userid'] = $desa;
                }
            } else {
                if (getSessionValue('ROLE') == 'admin' || isOperatorDesa()) {
                    $where_traffic['b.userid'] = getCurrentIdUser();
                }
            }

            if (isPMD()) {
                if (count($where_traffic_in) == 0) {
                    $where_traffic['(1=0) ='] = true;
                }
            }

            $bulanan = $this->trafficreport->getMonth($where_traffic, $where_traffic_in);

            $obj = new stdClass;
            $obj->y = bulan_indo($i);
            $obj->a = $bulanan != null ? $bulanan : 0;

            $monthly[$i] = $obj;
        }

        $data['report_monthly'] = json_encode($monthly);

        $where_infografis = "";
        if ($desa != null) {
            if (!isAdmin() && !isOperatorDesa()) {
                $where_infografis = "AND a.id_user = $desa";
            } else {
                $where_infografis = "AND a.id_user = " . getCurrentIdUser();
            }
        } else {
            if (isAdmin() || isOperatorDesa()) {
                $where_infografis = "AND a.id_user = " . getCurrentIdUser();
            } else {
                if (count($userid) > 0) {
                    $where_infografis = "AND a.id_user IN (" . implode(',', $userid) . ")";
                }
            }
        }

        if ($kabupatenget != null) {
            $where_infografis = " AND d.id_kabkota = $kabupatenget";
        } else {
            if (isPMD()) {
                $where_infografis = " AND d.id_kabkota = " . $user->kabkotaid;
            }
        }

        if ($kecamatanget != null) {
            $where_infografis = " AND d.id_kecamatan = $kecamatanget";
        } else {
            if (isPMD() || isKecamatan()) {
                $infografis_subdistricts = array();
                foreach ($data['kecamatan'] as $key => $value) {
                    $infografis_subdistricts[] = $value->id_kecamatan;
                }

                if (count($infografis_subdistricts) > 0) {
                    $where_infografis = " AND d.id_kecamatan IN (" . implode(',', $infografis_subdistricts) . ")";
                }
            }
        }

        if (isSuperAdmin() && !isAllPlatform()) {
            $where_infografis .= " AND c.platformname = '" . getCurrentPlatformName() . "'";
        }

        $infografis = $this->db->query("SELECT b.nama, COALESCE(SUM(a.jumlah), 0) AS jumlah FROM msinfografis a JOIN kategori_infografis b ON b.id = a.id_kategori JOIN msusers c ON c.id = a.id_user JOIN kelurahan d ON d.id_kelurahan = c.kelurahanid WHERE b.type = 'Pendidikan' $where_infografis GROUP BY b.nama ORDER BY b.nama")->result();

        $namainfografis = array();
        $datainfografis = array();
        $backgroundinfografis = array();
        $borderinfografis = array();

        foreach ($infografis as $key => $value) {
            $color = ColorGenerator::generate();
            $red = $color[0];
            $green = $color[1];
            $blue = $color[2];

            $namainfografis[] = $value->nama;
            if (isset(get_infografisprimary()[strtolower(preg_replace('/[^a-z]/i', '', $value->nama))])) {
                $wheresingleinfo = array();
                if ($desa != null) {
                    if (isAdmin() || isOperatorDesa()) {
                        $wheresingleinfo['id_user'] = getCurrentIdUser();
                    } else {
                        $wheresingleinfo['id_user'] = $desa;
                    }
                } else {
                    if (isAdmin() || isOperatorDesa()) {
                        $wheresingleinfo['id_user'] = getCurrentIdUser();
                    } else {
                        if (count($userid) > 0) {
                            $this->infografis->where_in('id_user', $userid);
                        }
                    }
                }

                if ($kabupatenget != null) {
                    $wheresingleinfo['c.id_kabkota'] = $kabupatenget;
                }

                if ($kecamatanget != null) {
                    $wheresingleinfo['c.id_kecamatan'] = $kecamatanget;
                }

                if (isSuperAdmin() && !isAllPlatform()) {
                    $wheresingleinfo['b.platformname'] = getCurrentPlatformName();
                }

                $infografissingle = $this->infografis->join('msusers b', 'b.id = a.id_user')
                    ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid')
                    ->result($wheresingleinfo);

                $jumlah = 0;
                foreach ($infografissingle as $k => $v) {
                    $kname = get_infografisprimary()[strtolower(preg_replace('/[^a-z]/i', '', $value->nama))];
                    $jumlah += $v->$kname;
                }

                $datainfografis[] = $jumlah;
            } else {
                $datainfografis[] = $value->jumlah;
            }
            $backgroundinfografis[] = "rgba($red, $green, $blue, 0.25)";
            $borderinfografis[] = "rgba($red, $green, $blue, 1)";
        }

        $data['infografis'] = json_encode($namainfografis);
        $data['backgroundinfografis'] = json_encode($backgroundinfografis);
        $data['borderinfografis'] = json_encode($borderinfografis);
        $data['datainfografis'] = json_encode($datainfografis);

        $infografiswork = $this->db->query("SELECT b.nama, COALESCE(SUM(a.jumlah), 0) AS jumlah FROM msinfografis a JOIN kategori_infografis b ON b.id = a.id_kategori JOIN msusers c ON c.id = a.id_user JOIN kelurahan d ON d.id_kelurahan = c.kelurahanid WHERE b.type = 'Pekerjaan' $where_infografis GROUP BY b.nama ORDER BY b.nama")->result();

        $namainfografiswork = array();
        $datainfografiswork = array();
        $backgroundinfografiswork = array();
        $borderinfografiswork = array();

        foreach ($infografiswork as $key => $value) {
            $color = ColorGenerator::generate();
            $red = $color[0];
            $green = $color[1];
            $blue = $color[2];

            $namainfografiswork[] = $value->nama;
            if (isset(get_infografisprimary()[strtolower(preg_replace('/[^a-z]/i', '', $value->nama))])) {
                $wheresingleinfo = array();
                if ($desa != null) {
                    if (isAdmin() || isOperatorDesa()) {
                        $wheresingleinfo['id_user'] = getCurrentIdUser();
                    } else {
                        $wheresingleinfo['id_user'] = $desa;
                    }
                } else {
                    if (isAdmin() || isOperatorDesa()) {
                        $wheresingleinfo['id_user'] = getCurrentIdUser();
                    } else {
                        if (count($userid) > 0) {
                            $this->infografis->where_in('id_user', $userid);
                        }
                    }
                }

                if ($kabupatenget != null) {
                    $wheresingleinfo['c.id_kabkota'] = $kabupatenget;
                }

                if ($kecamatanget != null) {
                    $wheresingleinfo['c.id_kecamatan'] = $kecamatanget;
                }

                if (isSuperAdmin() && !isAllPlatform()) {
                    $wheresingleinfo['b.platformname'] = getCurrentPlatformName();
                }

                $infografissingle = $this->infografis->join('msusers b', 'b.id = a.id_user')
                    ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid')
                    ->result($wheresingleinfo);

                $jumlah = 0;
                foreach ($infografissingle as $k => $v) {
                    $kname = get_infografisprimary()[strtolower(preg_replace('/[^a-z]/i', '', $value->nama))];
                    $jumlah += $v->$kname;
                }

                $datainfografiswork[] = $jumlah;
            } else {
                $datainfografiswork[] = $value->jumlah;
            }
            $backgroundinfografiswork[] = "rgba($red, $green, $blue, 0.25)";
            $borderinfografiswork[] = "rgba($red, $green, $blue, 1)";
        }

        $data['infografiswork'] = json_encode($namainfografiswork);
        $data['backgroundinfografiswork'] = json_encode($backgroundinfografiswork);
        $data['borderinfografiswork'] = json_encode($borderinfografiswork);
        $data['datainfografiswork'] = json_encode($datainfografiswork);

        $where_infogender = array();
        $where_in_infogender = array();
        if ($desa != null) {
            if (!isAdmin() && !isOperatorDesa()) {
                $where_infogender = array(
                    'id_user' => $desa
                );
            } else {
                $where_infogender = array(
                    'id_user' => getCurrentIdUser()
                );
            }
        } else {
            if (isPMD() && count($userid) > 0) {
                $this->infografis->where_in('id_user', $userid);
            } elseif (isAdmin() || isOperatorDesa()) {
                $where_infogender = array(
                    'id_user' => getCurrentIdUser()
                );
            }
        }

        if ($kabupatenget != null) {
            $where_infogender['c.id_kabkota'] = $kabupatenget;
        } else {
            if (isPMD()) {
                $where_infogender['c.id_kabkota'] = $user->kabkotaid;
            }
        }

        if ($kecamatanget != null) {
            $where_infogender['c.id_kecamatan'] = $kecamatanget;
        } else {
            if (isPMD() || isKecamatan()) {
                $infogender_subdistricts = array();
                foreach ($data['kecamatan'] as $key => $value) {
                    $infogender_subdistricts[] = $value->id_kecamatan;
                }

                if (count($infogender_subdistricts) > 0) {
                    $where_in_infogender['c.id_kecamatan'] = $infogender_subdistricts;
                }
            }
        }

        if (isSuperAdmin() && !isAllPlatform()) {
            $where_infogender['b.platformname'] = getCurrentPlatformName();
        }

        $data['lakilaki'] = $this->infografis->select('COALESCE(SUM(laki), 0) AS laki')
            ->join('msusers b', 'b.id = a.id_user')
            ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid')
            ->where_in($where_in_infogender)
            ->get($where_infogender)
            ->row()
            ->laki;

        if ($desa == null && isPMD() && count($userid) > 0) {
            $this->infografis->where_in('id_user', $userid);
        }

        $data['perempuan'] = $this->infografis->select('COALESCE(SUM(perempuan), 0) AS perempuan')
            ->join('msusers b', 'b.id = a.id_user')
            ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid')
            ->where_in($where_in_infogender)
            ->get($where_infogender)
            ->row()
            ->perempuan;

        $data['currentuser'] = $this->masterusers->get(array('id' => getCurrentIdUser()))->row();

        // Prepare desil data for chart
        $where_desil = array();
        $where_in_desil = array();
        if ($desa != null) {
            if (!isAdmin() && !isOperatorDesa()) {
                $where_desil = array(
                    'a.id_user' => $desa
                );
            } else {
                $where_desil = array(
                    'a.id_user' => getCurrentIdUser()
                );
            }
        } else {
            if (isPMD() && count($userid) > 0) {
                $where_in_desil['a.id_user'] = $userid;
            } elseif (isAdmin() || isOperatorDesa()) {
                $where_desil = array(
                    'a.id_user' => getCurrentIdUser()
                );
            }
        }

        if ($kabupatenget != null) {
            $where_desil['c.id_kabkota'] = $kabupatenget;
        } else {
            if (isPMD()) {
                $where_desil['c.id_kabkota'] = $user->kabkotaid;
            }
        }

        if ($kecamatanget != null) {
            $where_desil['c.id_kecamatan'] = $kecamatanget;
        } else {
            if (isPMD() || isKecamatan()) {
                $desil_subdistricts = array();
                foreach ($data['kecamatan'] as $key => $value) {
                    $desil_subdistricts[] = $value->id_kecamatan;
                }

                if (count($desil_subdistricts) > 0) {
                    $where_in_desil['c.id_kecamatan'] = $desil_subdistricts;
                }
            }
        }

        if (isSuperAdmin() && !isAllPlatform()) {
            $where_desil['b.platformname'] = getCurrentPlatformName();
        }

        // Get desil distribution data
        $desil_query = "SELECT a.desil, COUNT(*) as jumlah
                        FROM warga a
                        JOIN msusers b ON b.id = a.id_user
                        JOIN kelurahan c ON c.id_kelurahan = b.kelurahanid
                        WHERE 1=1";

        $desil_params = array();
        foreach ($where_desil as $key => $value) {
            $desil_query .= " AND $key = ?";
            $desil_params[] = $value;
        }

        if (!empty($where_in_desil)) {
            foreach ($where_in_desil as $key => $values) {
                if (is_array($values) && count($values) > 0) {
                    $placeholders = str_repeat('?,', count($values) - 1) . '?';
                    $desil_query .= " AND $key IN ($placeholders)";
                    $desil_params = array_merge($desil_params, $values);
                }
            }
        }

        $desil_query .= " GROUP BY a.desil ORDER BY a.desil";
        $desil_data = $this->db->query($desil_query, $desil_params)->result();

        // Prepare data for chart (ensure all desil 1-10 are represented)
        $desil_labels = array();
        $desil_counts = array();
        $desil_colors = array();

        for ($i = 1; $i <= 10; $i++) {
            $desil_labels[] = "Desil $i";
            $found = false;
            foreach ($desil_data as $item) {
                if ($item->desil == $i) {
                    $desil_counts[] = (int)$item->jumlah;
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                $desil_counts[] = 0;
            }

            // Color gradient from red (poor) to green (rich)
            $colors = [
                'rgba(220, 53, 69, 0.8)',   // Desil 1 - Red (Paling Miskin)
                'rgba(255, 99, 71, 0.8)',   // Desil 2 - Tomato
                'rgba(255, 140, 0, 0.8)',   // Desil 3 - Dark Orange
                'rgba(255, 165, 0, 0.8)',   // Desil 4 - Orange
                'rgba(255, 215, 0, 0.8)',   // Desil 5 - Gold
                'rgba(173, 255, 47, 0.8)',  // Desil 6 - Green Yellow
                'rgba(124, 252, 0, 0.8)',   // Desil 7 - Lawn Green
                'rgba(50, 205, 50, 0.8)',   // Desil 8 - Lime Green
                'rgba(34, 139, 34, 0.8)',   // Desil 9 - Forest Green
                'rgba(0, 100, 0, 0.8)'      // Desil 10 - Dark Green (Paling Kaya)
            ];
            $desil_colors[] = $colors[$i - 1];
        }

        $data['desil_labels'] = json_encode($desil_labels);
        $data['desil_counts'] = json_encode($desil_counts);
        $data['desil_colors'] = json_encode($desil_colors);

        if (!isBPD()) {
            $border = $this->db->query("select a.username, b.nama_kelurahan, c.nama_kecamatan, d.nama_kabkota, e.border, e.custom_border from msusers a left join kelurahan b on b.id_kelurahan = a.kelurahanid left join kecamatan c ON c.id_kecamatan = b.id_kecamatan left join kabkota d on d.id_kabkota = c.id_kabkota left join villagesborder e on e.village = b.nama_kelurahan and e.sub_district = c.nama_kecamatan where a.id = '" . getCurrentIdUser() . "' order by c.nama_kecamatan asc")->row();
        } else {
            $border = $this->db->query("select a.username, b.nama_kelurahan, c.nama_kecamatan, d.nama_kabkota, e.border, e.custom_border from msusers a left join kelurahan b on b.id_kelurahan = a.kelurahanid left join kecamatan c ON c.id_kecamatan = b.id_kecamatan left join kabkota d on d.id_kabkota = c.id_kabkota left join villagesborder e on e.village = b.nama_kelurahan and e.sub_district = c.nama_kecamatan where a.id = '" . $currentuser->userid . "' order by c.nama_kecamatan asc")->row();
        }

        if (($border->border ?? null) != null || ($border->custom_border ?? null) != null) {
            if ($border->custom_border != null) {
                $border_decoded = json_decode($border->custom_border);
            } else {
                $border_decoded = json_decode($border->border);
            }

            $coordinates = array();
            foreach ($border_decoded as $key => $value) {
                $coordinates[] = array(
                    'lat' => (float)$value[1],
                    'lng' => (float)$value[0]
                );
            }

            $data['coords'] = json_encode($coordinates);
            if (isset($coordinates[0]['lat']) && isset($coordinates[0]['lng']) && $coordinates[0]['lat'] != null && $coordinates[0]['lng'] != null) {
                $data['firstcoords'] = json_encode(array(
                    'lat' => $coordinates[0]['lat'] - 0.02,
                    'lng' => $coordinates[0]['lng'] + 0.03
                ));
                // $data['firstcoords'] = null;
            } else {
                $data['firstcoords'] = null;
            }
        } else {
            $data['coords'] = null;
        }

        $data['sliderpopup'] = $this->sliderpopup->result();

        // Add error handling data for display
        $data['error_message'] = $this->_getErrorMessage($error, $error_file, $error_details);

        return $this->load->view('master_v2/master', $data);
    }

    /**
     * Get user-friendly error message based on error code
     */
    private function _getErrorMessage($error, $error_file = null, $error_details = null)
    {
        if (!$error) {
            return null;
        }

        $messages = array(
            'phpword_validation_failed' => 'Terjadi kesalahan saat memvalidasi dokumen Word. File mungkin rusak atau tidak valid.',
            'ziparchive_error' => 'Terjadi kesalahan saat membaca file dokumen. File mungkin rusak atau tidak dapat diakses.',
            'ziparchive_processing_error' => 'Terjadi kesalahan saat memproses dokumen Word. File mungkin rusak atau format tidak didukung.',
            'invalid_word_document' => 'Dokumen Word tidak valid atau rusak. Silakan periksa file dan coba lagi.',
            'invalid_docx_format' => 'Format file DOCX tidak valid. File mungkin rusak atau bukan dokumen Word yang valid.',
            'invalid_doc_format' => 'Format file DOC tidak valid. File mungkin rusak atau bukan dokumen Word yang valid.',
            'cannot_read_doc_file' => 'Tidak dapat membaca file DOC. File mungkin rusak atau tidak dapat diakses.',
            'empty_letter_format' => 'Format surat tidak ditemukan. Silakan hubungi administrator.',
            'invalid_remote_url' => 'URL file tidak valid. Silakan hubungi administrator.',
            'file_download_failed' => 'Gagal mengunduh file template. Silakan coba lagi atau hubungi administrator.',
            'file_is_html_not_word' => 'File yang diunduh bukan dokumen Word yang valid. Silakan hubungi administrator.',
            'file_not_accessible' => 'File tidak dapat diakses. Silakan coba lagi atau hubungi administrator.',
            'empty_file' => 'File kosong atau tidak valid. Silakan hubungi administrator.',
            'template_not_found' => 'Template dokumen tidak ditemukan. Silakan hubungi administrator.'
        );

        $message = isset($messages[$error]) ? $messages[$error] : 'Terjadi kesalahan yang tidak diketahui.';

        // Add file information if available
        if ($error_file) {
            $message .= ' (File: ' . htmlspecialchars($error_file) . ')';
        }

        // Log the error for debugging
        error_log("Dashboard error display - Error: $error, File: $error_file, Details: $error_details");

        return $message;
    }

    public function desa()
    {
        $userid = array();
        if (isPMD() || isKecamatan()) {
            $user = $this->masterusers->getDefaultData(array('id' => getCurrentIdUser()))->row();

            $kecamatan = $user->kecamatanid;

            if ($kecamatan == null) {
                $kelurahan = $this->db->get_where('kelurahan', array(
                    'id_kabkota' => $user->kabkotaid
                ))->result();

                $data['kecamatan'] = $this->db->where_in('id_kabkota', $user->kabkotaid)
                    ->get_where('kecamatan')
                    ->result();
            } else {
                $kecamatan = explode(',', $kecamatan);
                $kelurahan = $this->db->where_in('id_kecamatan', $kecamatan)
                    ->get_where('kelurahan', array(
                        'id_kabkota' => $user->kabkotaid
                    ))->result();

                $data['kecamatan'] = $this->db->where_in('id_kecamatan', $kecamatan)
                    ->get_where('kecamatan')
                    ->result();
            }

            $kelurahanid = array();
            foreach ($kelurahan as $key => $value) {
                $kelurahanid[] = $value->id_kelurahan;
            }

            $users = $this->db->where_in('kelurahanid', $kelurahanid)->get('msusers')->result();

            foreach ($users as $key => $value) {
                $userid[] = $value->id;
            }
        } else {
            if (isAdmin()) {
                $userid[] = getCurrentIdUser();
            }
        }

        $kec = getPost('kecamatan');
        $desa = getPost('desa');
        $where = array('role' => 'admin');

        if ($kec != null) {
            $where['c.id_kecamatan'] = $kec;
        }

        if ($desa != null) {
            $where['a.id'] = $desa;
        }

        $data = array();
        $data['desa'] = $this->masterusers->select('a.*, c.nama_kelurahan AS desa, COALESCE(d.total, 0) AS perangkatdesa, COALESCE(e.total, 0) AS pengunjungbulanan')
            ->join('setting_umum b', 'b.id_user = a.id', 'LEFT')
            ->join('kelurahan c', 'c.id_kelurahan = a.kelurahanid', 'LEFT')
            ->join("(SELECT userid, COUNT(*) AS total FROM strukturorganisasi GROUP BY userid) d", 'd.userid = a.id', 'LEFT')
            ->join("(SELECT userid, COUNT(*) AS total FROM traffic WHERE MONTH(createddate) = MONTH(NOW()) AND YEAR(createddate) = YEAR(NOW()) GROUP BY userid) e", 'e.userid = a.id', 'LEFT')
            ->where_in('a.id', $userid)
            ->result($where);

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('dashboard/desa', $data, true)
        ));
    }

    public function download_pemerintah_kabupaten()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $currentuser = $this->masterusers->get(array('id' => getCurrentIdUser()))->row();
        $kecamatan = $currentuser->kecamatanid;

        if ($kecamatan != null) {
            $kecamatan = explode(',', $kecamatan);
            $kelurahan = $this->db->where_in('id_kecamatan', $kecamatan)
                ->get_where('kelurahan', array(
                    'id_kabkota' => $currentuser->kabkotaid
                ))->result();
        } else {
            $kelurahan = $this->db->get_where('kelurahan', array(
                'id_kabkota' => $currentuser->kabkotaid
            ))->result();
        }

        $kelurahanid = array();
        foreach ($kelurahan as $key => $value) {
            $kelurahanid[] = $value->id_kelurahan;
        }

        $users = $this->db->where_in('kelurahanid', $kelurahanid)->get('msusers')->result();

        $userid = array();
        foreach ($users as $key => $value) {
            $userid[] = $value->id;
        }

        $where_in = array();
        $where_in['a.id'] = $userid;

        $data = array();
        $data['rank_month'] = $this->trafficreport->getRankMonth(array(), $where_in);

        $pdf = new Html2Pdf('P');
        $pdf->setTestTdInOnePage(false);

        $pdf->writeHTML($this->load->view('dashboard/download_pemerintah_kabupaten', $data, true));
        $pdf->output('Struktur Organisasi se-Kabupaten.pdf');
    }

    public function download_pemerintah_kecamatan($kecamatanid)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $currentuser = $this->masterusers->get(array('id' => getCurrentIdUser()))->row();
        $kecamatan = $currentuser->kecamatanid;

        if ($kecamatan != null) {
            $kecamatan = explode(',', $kecamatan);
            $kelurahan = $this->db->where_in('id_kecamatan', $kecamatan)
                ->get_where('kelurahan', array(
                    'id_kabkota' => $currentuser->kabkotaid
                ))->result();
        } else {
            $kelurahan = $this->db->get_where('kelurahan', array(
                'id_kabkota' => $currentuser->kabkotaid
            ))->result();
        }

        $kelurahanid = array();
        foreach ($kelurahan as $key => $value) {
            $kelurahanid[] = $value->id_kelurahan;
        }

        $users = $this->db->where_in('kelurahanid', $kelurahanid)->get('msusers')->result();

        $userid = array();
        foreach ($users as $key => $value) {
            $userid[] = $value->id;
        }

        $where_in = array();
        $where_in['a.id'] = $userid;

        $data = array();
        $data['rank_month'] = $this->trafficreport->getRankMonth(array('c.id_kecamatan' => $kecamatanid), $where_in);

        $pdf = new Html2Pdf('P');
        $pdf->setTestTdInOnePage(false);

        $pdf->writeHTML($this->load->view('dashboard/download_pemerintah_kecamatan', $data, true));
        $pdf->output('Struktur Organisasi se-Kecamatan.pdf');
    }

    public function perangkat()
    {
        $id = getPost('id');

        $data = array();
        $data['desa'] = $this->masterusers->select('b.nama_kelurahan')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
            ->get(array('a.id' => $id))
            ->row();
        $data['perangkat'] = $this->strukturorganisasi->get(array('userid' => $id))->result();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('dashboard/perangkat', $data, true)
        ));
    }

    public function datatables_pengaduan()
    {
        $currentuser = getCurrentUser();
        $data = array();
        $bidangpengaduan = getPost('bidangpengaduan');

        $datatables = $this->datatables->make('Arsip_Pengaduan', 'QueryDatatables', 'SearchDatatables');

        $where = array(
            'a.userid' => $currentuser->userid
        );

        if ($bidangpengaduan != null) {
            $where['a.bidangpengaduanid'] = $bidangpengaduan;
        }

        foreach ($datatables->getData($where) as $key => $value) {
            if ($value->dokumen != null) {
                $actions = "<a href=\"" . asset_url($value->dokumen) . "\" target=\"_blank\" class=\"btn btn-primary btn-sm\"><i class=\"fa fa-file\"></i></a>";
            } else {
                $actions = "N/A";
            }

            $detail = array();
            $detail[] = date('d F Y H:i:s', strtotime($value->tanggalpengaduan));
            $detail[] = $value->bidangpengaduan ?? '-';
            $detail[] = $value->namawarga ?? '-';
            $detail[] = $value->isi;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function export_pengaduan()
    {
        $bidang = getGet('bidang');
        $currentuser = getCurrentUser();

        $where = array(
            'userid' => $currentuser->userid
        );

        if ($bidang != null) {
            $where['a.id'] = $bidang;
        }

        $get_setting = $this->settingumum->getDefaultData(array('a.id_user' => $currentuser->userid))->row();
        $bidangpengaduan = $this->bidangpengaduans->result($where);

        $kabupaten = $this->masterusers->select('c.nama_kabkota, c.logo')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
            ->join('kabkota c', 'c.id_kabkota = b.id_kabkota')
            ->get(array('a.id' => $currentuser->userid))
            ->row();

        $pdf = new Html2Pdf();
        $pdf->setTestTdInOnePage(false);
        $pdf->writeHTML($this->load->view('dashboard/export_pengaduan', array(
            'setting' => $get_setting,
            'bidangpengaduan' => $bidangpengaduan,
            'userid' => $this->subdomain_account->id,
            'kabupaten' => $kabupaten
        ), true));
        $pdf->output('Arsip Pengaduan.pdf');
    }

    public function pengaduan()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        } else if (!isBPD()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $type = getPost('type');
        $currentuser = getCurrentUser();

        $type_alias = "";
        if ($type == 'today') {
            $type_alias = "Hari Ini";

            $pengaduan = $this->arsippengaduan->select('a.*, b.name AS bidangpengaduan, c.nama AS namawarga')
                ->join('bidangpengaduan b', 'b.id = a.bidangpengaduanid')
                ->join('warga c', 'c.nik = a.wargaid')
                ->get(array(
                    'DATE(a.tanggalpengaduan)' => date('Y-m-d'),
                    'a.userid' => $currentuser->userid
                ))->result();
        } else if ($type == 'monthly') {
            $type_alias = "Bulan Ini";

            $pengaduan = $this->arsippengaduan->select('a.*, b.name AS bidangpengaduan, c.nama AS namawarga')
                ->join('bidangpengaduan b', 'b.id = a.bidangpengaduanid')
                ->join('warga c', 'c.nik = a.wargaid')
                ->get(array(
                    'a.userid' => $currentuser->userid,
                    'MONTH(a.tanggalpengaduan)' => date('m'),
                    'YEAR(a.tanggalpengaduan)' => date('Y')
                ))->result();
        } else if ($type == 'yearly') {
            $type_alias = "Tahun Ini";

            $pengaduan = $this->arsippengaduan->select('a.*, b.name AS bidangpengaduan, c.nama AS namawarga')
                ->join('bidangpengaduan b', 'b.id = a.bidangpengaduanid')
                ->join('warga c', 'c.nik = a.wargaid')
                ->get(array(
                    'a.userid' => $currentuser->userid,
                    'YEAR(a.tanggalpengaduan)' => date('Y')
                ))->result();
        } else {
            $type_alias = "Semua";

            $pengaduan = $this->arsippengaduan->select('a.*, b.name AS bidangpengaduan, c.nama AS namawarga')
                ->join('bidangpengaduan b', 'b.id = a.bidangpengaduanid')
                ->join('warga c', 'c.nik = a.wargaid')
                ->result(array(
                    'a.userid' => $currentuser->userid
                ));
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('dashboard/pengaduan', array(
                'type' => $type_alias,
                'pengaduan' => $pengaduan
            ), true)
        ));
    }

    public function perangkat_log()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        } else if (!isPMD() && !isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->strukturorganisasi->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('dashboard/perangkat_log', array(
                'log' => $this->logstrukturorganisasi->order_by('a.createddate', 'DESC')
                    ->result(array(
                        'strukturorganisasiid' => $id
                    ))
            ), true)
        ));
    }
}
