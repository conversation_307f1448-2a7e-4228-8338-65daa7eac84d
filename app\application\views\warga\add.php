<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!-- ============================================================== -->
<!-- Container fluid  -->
<!-- ============================================================== -->
<div class="container-fluid">
	<div class="row">
		<div class="col-md-12">
			<div class="card">
				<div class="card-header">
					<h4 class="card-title">Tambah Data Warga</h4>
				</div>

				<form id="frmAddWarga" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off" enctype="multipart/form-data">
					<div class="card-body">
						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>NIK <span class="text-danger">*</span></label>
									<input type="number" name="nik" class="form-control" placeholder="NIK" required>
								</div>
							</div>

							<div class="col-md-6">
								<div class="form-group">
									<label>Nomor KK <span class="text-danger">*</span></label>
									<input type="number" name="nomor_kk" class="form-control" placeholder="Nomor KK" required>
								</div>
							</div>
						</div>

						<div class="form-group">
							<label>Nama <span class="text-danger">*</span></label>
							<input type="text" name="nama" class="form-control" placeholder="Nama" required>
						</div>

						<div class="form-group">
							<label>Alamat</label>
							<textarea name="alamat" required class="form-control" placeholder="Alamat"></textarea>
						</div>

						<div class="row">
							<div class="col-md-4">
								<div class="form-group">
									<label>RT <span class="text-danger">*</span></label>
									<input type="text" name="rt" class="form-control" placeholder="RT" required>
								</div>
							</div>

							<div class="col-md-4">
								<div class="form-group">
									<label>RW <span class="text-danger">*</span></label>
									<input type="text" name="rw" class="form-control" placeholder="RW" required>
								</div>
							</div>

							<div class="col-md-4">
								<div class="form-group">
									<label>Kode POS <span class="text-danger">*</span></label>
									<input type="number" name="kode_pos" class="form-control" placeholder="Kode POS" required>
								</div>
							</div>
						</div>

						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>Jenis Kelamin <span class="text-danger">*</span></label>
									<div class="custom-control custom-radio">
										<input type="radio" id="laki" name="jenis_kelamin" value="Laki-laki" class="custom-control-input" checked>
										<label class="custom-control-label" for="laki">Laki laki</label>
									</div>

									<div class="custom-control custom-radio">
										<input type="radio" id="cewe" name="jenis_kelamin" value="Perempuan" class="custom-control-input">
										<label class="custom-control-label" for="cewe">Perempuan</label>
									</div>
								</div>
							</div>

							<div class="col-md-6">
								<div class="form-group">
									<label>Golongan Darah <span class="text-danger">*</span></label>
									<select name="golongandarah" class="form-control" required>
										<option value="">- Pilih -</option>
										<option value="O">O</option>
										<option value="A">A</option>
										<option value="B">B</option>
										<option value="AB">AB</option>
										<option value="Tidak Tahu">Tidak Tahu</option>
									</select>
								</div>
							</div>
						</div>

						<label>TTL <span class="text-danger">*</span></label>
						<div class="row">
							<div class="col-md-4">
								<div class="form-group">
									<input type="text" name="tempat_lahir" class="form-control" placeholder="Tempat Lahir" required>
								</div>
							</div>

							<div class="col-md-4">
								<div class="form-group">
									<input type="date" name="tanggal_lahir" class="form-control" placeholder="Tanggal Lahir" required>
								</div>
							</div>

							<div class="col-md-4">
								<div class="form-group">
									<label>Agama <span class="text-danger">*</span></label>
									<select class="form-control" name="agama" required>
										<option value="Islam">Islam</option>
										<option value="Kristen">Kristen</option>
										<option value="Katholik">Katholik</option>
										<option value="Budha">Budha</option>
										<option value="Hindu">Hindu</option>
										<option value="Konghucu">Konghucu</option>
										<option value="Tidak Tahu">Tidak Tahu</option>
									</select>
								</div>
							</div>
						</div>

						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>Pendidikan <span class="text-danger">*</span></label>
									<select name="pendidikan" class="form-control" required>
										<option value="">- Pilih -</option>
										<?php foreach (pendidikanTerakhir() as $key => $value) : ?>
											<option value="<?= $value ?>"><?= $value ?></option>
										<?php endforeach; ?>
										<option value="Tidak Tahu">Tidak Tahu</option>
									</select>
								</div>
							</div>

							<div class="col-md-6">
								<div class="form-group">
									<label>Pekerjaan <span class="text-danger">*</span></label>
									<select name="pekerjaan" class="form-control" required>
										<option value="">- Pilih -</option>
										<?php foreach (pekerjaanPokok() as $key => $value) : ?>
											<option value="<?= $value ?>"><?= $value ?></option>
										<?php endforeach; ?>
										<option value="Tidak Tahu">Tidak Tahu</option>
									</select>
								</div>
							</div>
						</div>

						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>Status Perkawinan <span class="text-danger">*</span></label>

									<div class="row">
										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="kawin" name="status_perkawinan" value="Kawin" class="custom-control-input" checked>
												<label class="custom-control-label" for="kawin">Kawin</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="belum" name="status_perkawinan" value="Belum Kawin" class="custom-control-input">
												<label class="custom-control-label" for="belum">Belum Kawin</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="janda" name="status_perkawinan" value="Janda/Duda" class="custom-control-input">
												<label class="custom-control-label" for="janda">Janda/Duda</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="ceraihidup" name="status_perkawinan" value="Cerai Hidup" class="custom-control-input">
												<label class="custom-control-label" for="ceraihidup">Cerai Hidup</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="ceraimati" name="status_perkawinan" value="Cerai Mati" class="custom-control-input">
												<label class="custom-control-label" for="ceraimati">Cerai Mati</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="tidaktahu_statusperkawinan" name="status_perkawinan" value="Tidak Tahu" class="custom-control-input">
												<label class="custom-control-label" for="tidaktahu_statusperkawinan">Tidak Tahu</label>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="col-md-6">
								<div class="form-group">
									<label>Status Hubungan Dalam Keluarga <span class="text-danger">*</span></label>

									<div class="row">
										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="adik" name="status_hubungan_dalam_keluarga" value="Adik" class="custom-control-input" checked>
												<label class="custom-control-label" for="adik">Adik</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="anak_angkat" name="status_hubungan_dalam_keluarga" value="Anak Angkat" class="custom-control-input">
												<label class="custom-control-label" for="anak_angkat">Anak Angkat</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="anak_kandung" name="status_hubungan_dalam_keluarga" value="Anak Kandung" class="custom-control-input">
												<label class="custom-control-label" for="anak_kandung">Anak Kandung</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="anak_tiri" name="status_hubungan_dalam_keluarga" value="Anak Tiri" class="custom-control-input">
												<label class="custom-control-label" for="anak_tiri">Anak Tiri</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="ayah" name="status_hubungan_dalam_keluarga" value="Ayah" class="custom-control-input">
												<label class="custom-control-label" for="ayah">Ayah</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="cucu" name="status_hubungan_dalam_keluarga" value="Cucu" class="custom-control-input">
												<label class="custom-control-label" for="cucu">Cucu</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="famili_lain" name="status_hubungan_dalam_keluarga" value="Famili Lain" class="custom-control-input">
												<label class="custom-control-label" for="famili_lain">Famili Lain</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="ibu" name="status_hubungan_dalam_keluarga" value="Ibu" class="custom-control-input">
												<label class="custom-control-label" for="ibu">Ibu</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="istri" name="status_hubungan_dalam_keluarga" value="Istri" class="custom-control-input">
												<label class="custom-control-label" for="istri">Istri</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="kakak" name="status_hubungan_dalam_keluarga" value="Kakak" class="custom-control-input">
												<label class="custom-control-label" for="kakak">Kakak</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="kakek" name="status_hubungan_dalam_keluarga" value="Kakek" class="custom-control-input">
												<label class="custom-control-label" for="kakek">Kakek</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="kepala" name="status_hubungan_dalam_keluarga" value="Kepala Keluarga" class="custom-control-input">
												<label class="custom-control-label" for="kepala">Kepala Keluarga</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="keponakan" name="status_hubungan_dalam_keluarga" value="Keponakan" class="custom-control-input">
												<label class="custom-control-label" for="keponakan">Keponakan</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="lainnya" name="status_hubungan_dalam_keluarga" value="Lainnya" class="custom-control-input">
												<label class="custom-control-label" for="lainnya">Lainnya</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="menantu" name="status_hubungan_dalam_keluarga" value="Menantu" class="custom-control-input">
												<label class="custom-control-label" for="menantu">Menantu</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="mertua" name="status_hubungan_dalam_keluarga" value="Mertua" class="custom-control-input">
												<label class="custom-control-label" for="mertua">Mertua</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="nenek" name="status_hubungan_dalam_keluarga" value="Nenek" class="custom-control-input">
												<label class="custom-control-label" for="nenek">Nenek</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="paman" name="status_hubungan_dalam_keluarga" value="Paman" class="custom-control-input">
												<label class="custom-control-label" for="paman">Paman</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="sepupu" name="status_hubungan_dalam_keluarga" value="Sepupu" class="custom-control-input">
												<label class="custom-control-label" for="sepupu">Sepupu</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="suami" name="status_hubungan_dalam_keluarga" value="Suami" class="custom-control-input">
												<label class="custom-control-label" for="suami">Suami</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="tante" name="status_hubungan_dalam_keluarga" value="Tante" class="custom-control-input">
												<label class="custom-control-label" for="tante">Tante</label>
											</div>
										</div>

										<div class="col-md-4">
											<div class="custom-control custom-radio">
												<input type="radio" id="teman" name="status_hubungan_dalam_keluarga" value="Teman" class="custom-control-input">
												<label class="custom-control-label" for="teman">Teman</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>Kewarganegaraan <span class="text-danger">*</span></label>
									<select name="kewarganegaraan" class="form-control" required>
										<option value="">- Pilih -</option>
										<option value="Warga Negara Indonesia">Warga Negara Indonesia</option>
										<option value="Warga Negara Asing">Warga Negara Asing</option>
										<option value="Dwi Kewarganegaraan">Dwi Kewarganegaraan</option>
									</select>
								</div>
							</div>

							<div class="col-md-6">
								<div class="form-group">
									<label>Akseptor KB <span class="text-danger">*</span></label>
									<select name="akseptorkb" class="form-control" required>
										<option value="">- Pilih -</option>
										<?php foreach (akseptorKB() as $key => $value) : ?>
											<option value="<?= $value ?>"><?= $value ?></option>
										<?php endforeach; ?>
										<option value="Tidak Tahu">Tidak Tahu</option>
									</select>
								</div>
							</div>
						</div>

						<div class="form-group">
							<label>Desil <span class="text-danger">*</span></label>
							<div class="row">
								<div class="col-md-2">
									<div class="custom-control custom-radio">
										<input type="radio" id="desil1" name="desil" value="1" class="custom-control-input" checked>
										<label class="custom-control-label" for="desil1">1</label>
									</div>
								</div>
								<div class="col-md-2">
									<div class="custom-control custom-radio">
										<input type="radio" id="desil2" name="desil" value="2" class="custom-control-input">
										<label class="custom-control-label" for="desil2">2</label>
									</div>
								</div>
								<div class="col-md-2">
									<div class="custom-control custom-radio">
										<input type="radio" id="desil3" name="desil" value="3" class="custom-control-input">
										<label class="custom-control-label" for="desil3">3</label>
									</div>
								</div>
								<div class="col-md-2">
									<div class="custom-control custom-radio">
										<input type="radio" id="desil4" name="desil" value="4" class="custom-control-input">
										<label class="custom-control-label" for="desil4">4</label>
									</div>
								</div>
								<div class="col-md-2">
									<div class="custom-control custom-radio">
										<input type="radio" id="desil5" name="desil" value="5" class="custom-control-input">
										<label class="custom-control-label" for="desil5">5</label>
									</div>
								</div>
								<div class="col-md-2">
									<div class="custom-control custom-radio">
										<input type="radio" id="desil6" name="desil" value="6" class="custom-control-input">
										<label class="custom-control-label" for="desil6">6</label>
									</div>
								</div>
							</div>
							<div class="row mt-2">
								<div class="col-md-2">
									<div class="custom-control custom-radio">
										<input type="radio" id="desil7" name="desil" value="7" class="custom-control-input">
										<label class="custom-control-label" for="desil7">7</label>
									</div>
								</div>
								<div class="col-md-2">
									<div class="custom-control custom-radio">
										<input type="radio" id="desil8" name="desil" value="8" class="custom-control-input">
										<label class="custom-control-label" for="desil8">8</label>
									</div>
								</div>
								<div class="col-md-2">
									<div class="custom-control custom-radio">
										<input type="radio" id="desil9" name="desil" value="9" class="custom-control-input">
										<label class="custom-control-label" for="desil9">9</label>
									</div>
								</div>
								<div class="col-md-2">
									<div class="custom-control custom-radio">
										<input type="radio" id="desil10" name="desil" value="10" class="custom-control-input">
										<label class="custom-control-label" for="desil10">10</label>
									</div>
								</div>
							</div>
						</div>

						<div class="form-group">
							<label>Kode RFID</label>
							<input type="text" name="rfid" class="form-control" placeholder="Kode RFID">
						</div>

						<div class="form-group">
							<label>Status <span class="text-danger">*</span></label>
							<select name="status" class="form-control" required>
								<option value="Aktif">Aktif</option>
								<option value="Pindah">Pindah</option>
								<option value="Meninggal">Meninggal</option>
								<option value="Cerai Mati">Cerai Mati</option>
								<option value="Cerai Hidup">Cerai Hidup</option>
							</select>
						</div>
					</div>

					<div class="card-footer">
						<div class="float-right">
							<a href="<?= base_url('warga') ?>" class="btn btn-danger">Kembali</a>
							<button type="submit" class="btn btn-primary">Simpan</button>
						</div>

						<div class="clearfix"></div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
<!-- ============================================================== -->
<!-- Container fluid  -->
<!-- ============================================================== -->

<script>
	window.onload = function() {
		$('#frmAddWarga').submit(function(e) {
			e.preventDefault();

			let formData = new FormData(this);
			let elementsForm = $(this).find('button, input, textarea, select');

			elementsForm.attr('disabled', true);

			$.ajax({
				url: $(this).attr('action'),
				method: $(this).attr('method'),
				dataType: 'json',
				data: formData,
				processData: false,
				contentType: false,
				success: function(response) {
					elementsForm.removeAttr('disabled');

					if (response.RESULT == 'OK') {
						return swalMessageSuccess(response.MESSAGE, ok => {
							window.location.href = "<?= base_url('warga') ?>";
						});
					} else {
						return swalMessageFailed(response.MESSAGE);
					}
				}
			}).fail(function() {
				elementsForm.removeAttr('disabled');

				return swalError();
			})
		});
	};
</script>