<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Survey Kepuasan Masyarakat - <?= $setting->desa ?? 'Desa' ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .survey-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .survey-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .survey-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translate(-50%, -50%) rotate(0deg);
            }

            100% {
                transform: translate(-50%, -50%) rotate(360deg);
            }
        }

        .survey-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }

        .survey-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .survey-body {
            padding: 40px;
        }

        .section-title {
            color: #2c3e50;
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 3px solid #4facfe;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 50px;
            height: 3px;
            background: #00f2fe;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .form-control,
        .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .form-control:focus,
        .form-select:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        }

        .radio-group {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #4facfe;
            transition: all 0.3s ease;
        }

        .radio-group:hover {
            background: #e3f2fd;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .question-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1rem;
        }

        .radio-option {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .radio-option:hover {
            border-color: #4facfe;
            background: #f0f8ff;
        }

        .radio-option input[type="radio"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .radio-option input[type="radio"]:checked+label {
            color: #4facfe;
            font-weight: 500;
        }

        .radio-option:has(input[type="radio"]:checked) {
            border-color: #4facfe;
            background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%);
        }

        .btn-submit {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.6);
            color: white;
        }

        .btn-submit:disabled {
            opacity: 0.7;
            transform: none;
        }

        .progress-bar-custom {
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            margin-bottom: 30px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 3px;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50px;
            padding: 10px 20px;
            color: #2c3e50;
            text-decoration: none;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .back-btn:hover {
            background: white;
            color: #4facfe;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 768px) {
            .survey-container {
                margin: 10px;
                border-radius: 15px;
            }

            .survey-header {
                padding: 30px 20px;
            }

            .survey-header h1 {
                font-size: 2rem;
            }

            .survey-body {
                padding: 30px 20px;
            }
        }
    </style>
</head>

<body>
    <a href="<?= base_url() ?>" class="back-btn">
        <i class="fas fa-arrow-left me-2"></i>Kembali
    </a>

    <div class="survey-container">
        <!-- Header -->
        <div class="survey-header">
            <h1><i class="fas fa-poll me-3"></i>Survey Kepuasan Masyarakat</h1>
            <p>Pada Unit Pelayanan Publik <?= strtoupper($setting->desa ?? 'DESA') ?></p>
        </div>

        <!-- Body -->
        <div class="survey-body">
            <!-- Progress Bar -->
            <div class="progress-bar-custom">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <form id="surveyForm" method="POST" action="<?= base_url('survey/kepuasan/submit') ?>">
                <!-- PROFIL RESPONDEN -->
                <div class="mb-5">
                    <h2 class="section-title">
                        <i class="fas fa-user me-2"></i>Profil Responden
                    </h2>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Jenis Kelamin <span class="text-danger">*</span></label>
                                <div class="d-flex gap-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="jenis_kelamin" id="jk_l" value="L" required>
                                        <label class="form-check-label" for="jk_l">
                                            <i class="fas fa-mars me-1"></i>Laki-laki
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="jenis_kelamin" id="jk_p" value="P" required>
                                        <label class="form-check-label" for="jk_p">
                                            <i class="fas fa-venus me-1"></i>Perempuan
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="pendidikan" class="form-label">Pendidikan <span class="text-danger">*</span></label>
                                <select class="form-control" id="pendidikan" name="pendidikan" required>
                                    <option value="">Pilih Pendidikan</option>
                                    <option value="SD">SD</option>
                                    <option value="SMP">SMP</option>
                                    <option value="SMA">SMA</option>
                                    <option value="S1">S1</option>
                                    <option value="S2">S2</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="usia" class="form-label">Usia <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="usia" name="usia" min="17" max="100" required>
                                    <span class="input-group-text">tahun</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="pekerjaan" class="form-label">Pekerjaan <span class="text-danger">*</span></label>
                                <select class="form-control" id="pekerjaan" name="pekerjaan" required>
                                    <option value="">Pilih Pekerjaan</option>
                                    <option value="PNS">PNS</option>
                                    <option value="TNI">TNI</option>
                                    <option value="POLRI">POLRI</option>
                                    <option value="SWASTA">Swasta</option>
                                    <option value="WIRAUSAHA">Wirausaha</option>
                                    <option value="LAINNYA">Lainnya</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6" id="pekerjaan_lainnya_group" style="display: none;">
                            <div class="form-group">
                                <label for="pekerjaan_lainnya" class="form-label">Sebutkan Pekerjaan Lainnya</label>
                                <input type="text" class="form-control" id="pekerjaan_lainnya" name="pekerjaan_lainnya" placeholder="Masukkan pekerjaan lainnya">
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="jenis_layanan" class="form-label">Jenis Layanan yang Diterima <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="jenis_layanan" name="jenis_layanan" rows="3" required placeholder="Contoh: Surat Keterangan Domisili, Surat Keterangan Usaha, dll."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- PENDAPAT RESPONDEN -->
                <div class="mb-5">
                    <h2 class="section-title">
                        <i class="fas fa-comments me-2"></i>Pendapat Responden Tentang Pelayanan
                    </h2>
                    <p class="text-muted mb-4">Berikan penilaian Anda terhadap kualitas pelayanan yang diterima</p>

                    <?php
                    $questions = array(
                        'u1' => array(
                            'title' => 'Kesesuaian persyaratan pelayanan dengan jenis pelayanannya',
                            'options' => array('Tidak sesuai', 'Kurang sesuai', 'Sesuai', 'Sangat sesuai'),
                            'icon' => 'fas fa-clipboard-list'
                        ),
                        'u2' => array(
                            'title' => 'Kemudahan prosedur pelayanan di unit ini',
                            'options' => array('Tidak mudah', 'Kurang mudah', 'Mudah', 'Sangat mudah'),
                            'icon' => 'fas fa-route'
                        ),
                        'u3' => array(
                            'title' => 'Kecepatan waktu dalam memberikan pelayanan',
                            'options' => array('Tidak cepat', 'Kurang cepat', 'Cepat', 'Sangat cepat'),
                            'icon' => 'fas fa-clock'
                        ),
                        'u4' => array(
                            'title' => 'Kewajaran biaya/tarif dalam pelayanan',
                            'options' => array('Sangat mahal', 'Cukup mahal', 'Murah', 'Gratis'),
                            'icon' => 'fas fa-money-bill-wave'
                        ),
                        'u5' => array(
                            'title' => 'Kesesuaian produk pelayanan dengan standar yang ditetapkan',
                            'options' => array('Tidak sesuai', 'Kurang sesuai', 'Sesuai', 'Sangat sesuai'),
                            'icon' => 'fas fa-certificate'
                        ),
                        'u6' => array(
                            'title' => 'Kompetensi/kemampuan petugas dalam pelayanan',
                            'options' => array('Tidak kompeten', 'Kurang kompeten', 'Kompeten', 'Sangat kompeten'),
                            'icon' => 'fas fa-user-tie'
                        ),
                        'u7' => array(
                            'title' => 'Perilaku petugas dalam pelayanan (kesopanan dan keramahan)',
                            'options' => array('Tidak sopan dan ramah', 'Kurang sopan dan ramah', 'Sopan dan ramah', 'Sangat sopan dan ramah'),
                            'icon' => 'fas fa-smile'
                        ),
                        'u8' => array(
                            'title' => 'Kualitas sarana dan prasarana',
                            'options' => array('Buruk', 'Cukup', 'Baik', 'Sangat baik'),
                            'icon' => 'fas fa-building'
                        ),
                        'u9' => array(
                            'title' => 'Penanganan pengaduan pengguna layanan',
                            'options' => array('Tidak ada', 'Ada tetapi tidak berfungsi', 'Berfungsi kurang maksimal', 'Dikelola dengan baik'),
                            'icon' => 'fas fa-headset'
                        )
                    );

                    $counter = 1;
                    foreach ($questions as $key => $question): ?>
                        <div class="radio-group">
                            <div class="question-title">
                                <i class="<?= $question['icon'] ?> me-2 text-primary"></i>
                                <?= $counter ?>. <?= $question['title'] ?>
                            </div>
                            <div class="row">
                                <?php foreach ($question['options'] as $index => $option): ?>
                                    <div class="col-md-6 mb-2">
                                        <div class="radio-option">
                                            <input type="radio" name="<?= $key ?>" id="<?= $key ?>_<?= $index + 1 ?>" value="<?= $index + 1 ?>" required>
                                            <label for="<?= $key ?>_<?= $index + 1 ?>" class="mb-0">
                                                <?= chr(97 + $index) ?>. <?= $option ?>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php
                        $counter++;
                    endforeach; ?>
                </div>

                <div class="text-center">
                    <button type="submit" class="btn btn-submit">
                        <i class="fas fa-paper-plane me-2"></i>Kirim Survey
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Progress bar functionality
            const form = document.getElementById('surveyForm');
            const progressFill = document.getElementById('progressFill');
            const totalFields = form.querySelectorAll('input[required], select[required], textarea[required]').length;

            function updateProgress() {
                const filledFields = Array.from(form.querySelectorAll('input[required], select[required], textarea[required]')).filter(field => {
                    if (field.type === 'radio') {
                        return form.querySelector(`input[name="${field.name}"]:checked`);
                    }
                    return field.value.trim() !== '';
                }).length;

                const progress = (filledFields / totalFields) * 100;
                progressFill.style.width = progress + '%';
            }

            // Update progress on input change
            form.addEventListener('input', updateProgress);
            form.addEventListener('change', updateProgress);

            // Show/hide pekerjaan lainnya field
            const pekerjaanSelect = document.getElementById('pekerjaan');
            const pekerjaanLainnyaGroup = document.getElementById('pekerjaan_lainnya_group');
            const pekerjaanLainnyaInput = document.getElementById('pekerjaan_lainnya');

            pekerjaanSelect.addEventListener('change', function() {
                if (this.value === 'LAINNYA') {
                    pekerjaanLainnyaGroup.style.display = 'block';
                    pekerjaanLainnyaInput.required = true;
                } else {
                    pekerjaanLainnyaGroup.style.display = 'none';
                    pekerjaanLainnyaInput.required = false;
                    pekerjaanLainnyaInput.value = '';
                }
                updateProgress();
            });

            // Handle form submission
            $('#surveyForm').on('submit', function(e) {
                e.preventDefault();

                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: $(this).serialize(),
                    dataType: 'json',
                    beforeSend: function() {
                        $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Mengirim...');
                    },
                    success: function(response) {
                        if (response.RESULT === 'OK') {
                            swal({
                                title: 'Berhasil!',
                                text: response.MESSAGE,
                                icon: 'success',
                                button: 'OK'
                            }).then(function() {
                                window.location.href = '<?= base_url() ?>';
                            });
                        } else {
                            swal('Gagal!', response.MESSAGE, 'error');
                        }
                    },
                    error: function() {
                        swal('Error!', 'Terjadi kesalahan sistem. Silakan coba lagi.', 'error');
                    },
                    complete: function() {
                        $('button[type="submit"]').prop('disabled', false).html('<i class="fas fa-paper-plane me-2"></i>Kirim Survey');
                    }
                });
            });

            // Initial progress update
            updateProgress();
        });
    </script>
</body>

</html>