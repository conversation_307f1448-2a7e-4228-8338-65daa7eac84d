<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>

<div class="page-breadcrumb">
    <div class="row">
        <div class="col-7 align-self-center">
            <h3 class="page-title text-truncate text-dark font-weight-medium mb-1">Survey Perilaku</h3>
            <div class="d-flex align-items-center">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb m-0 p-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('admin') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Survey Perilaku</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Filter Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title">Filter Laporan</h4>
                    <form method="GET" action="<?= base_url('admin/survey-perilaku') ?>" class="row">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Tanggal Mulai</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter"></i> Filter
                            </button>
                            <a href="<?= base_url('admin/survey-perilaku') ?>" class="btn btn-secondary me-2">
                                <i class="fas fa-refresh"></i> Reset
                            </a>
                            <?php if ($statistics): ?>
                                <a href="<?= base_url('admin/survey-perilaku/export?' . http_build_query(array('start_date' => $start_date, 'end_date' => $end_date))) ?>" class="btn btn-success">
                                    <i class="fas fa-download"></i> Export Excel
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php if ($statistics): ?>
        <!-- Summary Cards -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="card border-left-primary">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Responden</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $statistics['total_responden'] ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card border-left-success">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Rata-rata NRR</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $statistics['avg_nrr'] ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card border-left-info">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">IKM Total</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $statistics['ikm_total'] ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-star fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card border-left-warning">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Kategori Mutu</div>
                                <div class="h6 mb-0 font-weight-bold text-gray-800"><?= $statistics['kategori_mutu'] ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-medal fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Results -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h4 class="card-title">Hasil Survey Per Unsur Perilaku</h4>
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered">
                                <thead class="table-dark">
                                    <tr>
                                        <th>No</th>
                                        <th>Unsur</th>
                                        <th>NRR</th>
                                        <th>IKM</th>
                                        <th>Kategori</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $counter = 1;
                                    foreach ($unsur_labels as $key => $label):
                                        $nrr = $statistics['nrr_per_unsur'][$key];
                                        $ikm = $statistics['ikm_per_unsur'][$key];

                                        $category_class = '';
                                        if ($ikm >= 81.26) {
                                            $category_class = 'text-success';
                                            $category_text = 'Sangat Baik';
                                        } elseif ($ikm >= 62.51) {
                                            $category_class = 'text-info';
                                            $category_text = 'Baik';
                                        } elseif ($ikm >= 43.76) {
                                            $category_class = 'text-warning';
                                            $category_text = 'Kurang Baik';
                                        } else {
                                            $category_class = 'text-danger';
                                            $category_text = 'Tidak Baik';
                                        }
                                    ?>
                                        <tr>
                                            <td><?= $counter ?></td>
                                            <td><?= $label ?></td>
                                            <td><?= $nrr ?></td>
                                            <td><?= $ikm ?></td>
                                            <td class="<?= $category_class ?> font-weight-bold"><?= $category_text ?></td>
                                        </tr>
                                    <?php
                                        $counter++;
                                    endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Interpretation Section -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h4 class="card-title"><i class="fas fa-chart-bar me-2"></i>Grafik IKM Per Unsur</h4>
                        <canvas id="ikmChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h4 class="card-title"><i class="fas fa-info-circle me-2"></i>Interpretasi Hasil IKM</h4>

                        <div class="alert alert-info">
                            <h6 class="mb-2"><i class="fas fa-scale-balanced me-2"></i>Kategori Mutu:</h6>
                            <ul class="mb-0">
                                <li><strong>A (Sangat Baik):</strong> 81,26 - 100</li>
                                <li><strong>B (Baik):</strong> 62,51 - 81,25</li>
                                <li><strong>C (Kurang Baik):</strong> 43,76 - 62,50</li>
                                <li><strong>D (Tidak Baik):</strong> 25,00 - 43,75</li>
                            </ul>
                        </div>

                        <div class="alert alert-success">
                            <h6 class="mb-2"><i class="fas fa-target me-2"></i>Target Kinerja:</h6>
                            <p class="mb-0">Nilai IKM minimal yang harus dicapai adalah <strong>62,51 (Kategori B)</strong> untuk memenuhi standar pelayanan yang baik.</p>
                        </div>

                        <div class="alert alert-warning">
                            <h6 class="mb-2"><i class="fas fa-lightbulb me-2"></i>Rekomendasi:</h6>
                            <ul class="mb-0">
                                <li>Prioritaskan perbaikan pada unsur dengan nilai IKM terendah.</li>
                                <li>Lakukan evaluasi berkala dan tindak lanjut rencana aksi setiap bulan.</li>
                                <li>Tingkatkan kedisiplinan dan integritas petugas pelayanan.</li>
                                <li>Perkuat kanal pengaduan dan transparansi proses layanan.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <?php else: ?>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-chart-bar fa-5x text-muted mb-4"></i>
                        <h4 class="text-muted">Belum Ada Data Survey</h4>
                        <p class="text-muted">Belum ada responden yang mengisi survey perilaku untuk periode yang dipilih.</p>
                        <a href="<?= base_url('survey/perilaku') ?>" class="btn btn-primary" target="_blank">
                            <i class="fas fa-external-link-alt"></i> Lihat Form Survey Perilaku
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php if ($statistics): ?>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const labels = <?= json_encode(array_values($unsur_labels)) ?>;
            const ikmData = <?= json_encode(array_values($statistics['ikm_per_unsur'])) ?>;

            const ctx = document.getElementById('ikmChart').getContext('2d');
            const ikmChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'IKM Score',
                        data: ikmData,
                        backgroundColor: 'rgba(24, 186, 177, 0.6)',
                        borderColor: 'rgba(24, 186, 177, 1)',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'IKM: ' + context.parsed.y.toFixed(2) + '%';
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
<?php endif; ?>