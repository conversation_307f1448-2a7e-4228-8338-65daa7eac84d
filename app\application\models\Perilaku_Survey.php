<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Perilaku_Survey extends MY_Model
{
    protected $table = 'perilaku_survey';

    public function __construct()
    {
        parent::__construct();
        $this->create_table_if_not_exists();
    }

    private function create_table_if_not_exists()
    {
        if (!$this->db->table_exists($this->table)) {
            $this->load->dbforge();

            $fields = array(
                'id' => array('type' => 'INT', 'constraint' => 11, 'unsigned' => TRUE, 'auto_increment' => TRUE),
                'desa_user_id' => array('type' => 'INT', 'constraint' => 11, 'unsigned' => TRUE),
                'jenis_kelamin' => array('type' => 'ENUM', 'constraint' => array('L', 'P')),
                'pendidikan' => array('type' => 'ENUM', 'constraint' => array('SD', 'SMP', 'SMA', 'S1', 'S2')),
                'usia' => array('type' => 'INT', 'constraint' => 3, 'unsigned' => TRUE),
                'pekerjaan' => array('type' => 'ENUM', 'constraint' => array('PNS', 'TNI', 'POLRI', 'SWASTA', 'WIRAUSAHA', 'LAINNYA')),
                'pekerjaan_lainnya' => array('type' => 'VARCHAR', 'constraint' => 255, 'null' => TRUE),
                'u1' => array('type' => 'TINYINT', 'constraint' => 1, 'unsigned' => TRUE),
                'u2' => array('type' => 'TINYINT', 'constraint' => 1, 'unsigned' => TRUE),
                'u3' => array('type' => 'TINYINT', 'constraint' => 1, 'unsigned' => TRUE),
                'u4' => array('type' => 'TINYINT', 'constraint' => 1, 'unsigned' => TRUE),
                'u5' => array('type' => 'TINYINT', 'constraint' => 1, 'unsigned' => TRUE),
                'u6' => array('type' => 'TINYINT', 'constraint' => 1, 'unsigned' => TRUE),
                'u7' => array('type' => 'TINYINT', 'constraint' => 1, 'unsigned' => TRUE),
                'u8' => array('type' => 'TINYINT', 'constraint' => 1, 'unsigned' => TRUE),
                'u9' => array('type' => 'TINYINT', 'constraint' => 1, 'unsigned' => TRUE),
                'u10' => array('type' => 'TINYINT', 'constraint' => 1, 'unsigned' => TRUE),
                'created_at' => array('type' => 'DATETIME', 'null' => FALSE)
            );

            $this->dbforge->add_field($fields);
            $this->dbforge->add_key('id', TRUE);
            $this->dbforge->add_key('desa_user_id');
            $this->dbforge->add_key('created_at');
            $this->dbforge->create_table($this->table, TRUE);
        }
    }

    public function get_survey_statistics($desa_user_id, $start_date = null, $end_date = null)
    {
        $this->db->where('desa_user_id', $desa_user_id);

        if ($start_date) {
            $this->db->where('created_at >=', $start_date . ' 00:00:00');
        }
        if ($end_date) {
            $this->db->where('created_at <=', $end_date . ' 23:59:59');
        }

        $surveys = $this->db->get($this->table)->result();
        if (empty($surveys)) {
            return null;
        }

        $total_responden = count($surveys);
        $unsur_totals = array();
        for ($i = 1; $i <= 10; $i++) {
            $unsur_totals["u$i"] = 0;
            foreach ($surveys as $survey) {
                $unsur_totals["u$i"] += $survey->{"u$i"};
            }
        }

        $nrr_per_unsur = array();
        $ikm_per_unsur = array();
        $total_nrr = 0;
        for ($i = 1; $i <= 10; $i++) {
            $nrr_per_unsur["u$i"] = round($unsur_totals["u$i"] / $total_responden, 2);
            $ikm_per_unsur["u$i"] = round($nrr_per_unsur["u$i"] * 25, 2);
            $total_nrr += $nrr_per_unsur["u$i"];
        }

        $avg_nrr = round($total_nrr / 10, 2);
        $ikm_total = round($avg_nrr * 25, 2);

        $kategori_mutu = '';
        if ($ikm_total >= 81.26) {
            $kategori_mutu = 'A (Sangat Baik)';
        } elseif ($ikm_total >= 62.51) {
            $kategori_mutu = 'B (Baik)';
        } elseif ($ikm_total >= 43.76) {
            $kategori_mutu = 'C (Kurang Baik)';
        } else {
            $kategori_mutu = 'D (Tidak Baik)';
        }

        return array(
            'total_responden' => $total_responden,
            'avg_nrr' => $avg_nrr,
            'ikm_total' => $ikm_total,
            'kategori_mutu' => $kategori_mutu,
            'nrr_per_unsur' => $nrr_per_unsur,
            'ikm_per_unsur' => $ikm_per_unsur,
            'surveys' => $surveys
        );
    }

    public function get_unsur_labels()
    {
        return array(
            'u1' => 'Memberikan Uang/Barang/Makanan',
            'u2' => 'Imbalan',
            'u3' => 'Besaran Uang',
            'u4' => 'Memberikan Sesuatu',
            'u5' => 'Dipersulit',
            'u6' => 'Jenis Pemberian',
            'u7' => 'Hubungan Kekerabatan',
            'u8' => 'Jasa Calo/Perantara',
            'u9' => 'Diluar Jam Kerja/Kantor',
            'u10' => 'Mendengar Terjadinya KKN'
        );
    }
}

