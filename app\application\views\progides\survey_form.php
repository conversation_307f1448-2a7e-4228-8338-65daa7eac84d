<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>

<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-primary to-primary-dark min-h-screen flex items-center">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=2074&q=80'); background-size: cover; background-position: center;"></div>
    </div>
    
    <div class="container mx-auto px-4 relative z-10 pt-20">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
                <!-- Header -->
                <div class="bg-primary text-white p-6 text-center">
                    <h1 class="text-2xl md:text-3xl font-bold mb-2">SURVEY KEPUASAN MASYARAKAT (SKM)</h1>
                    <p class="text-primary-light">PADA UNIT PELAYANAN PUBLIK <?= strtoupper($setting->desa ?? 'DESA') ?></p>
                </div>
                
                <!-- Form Content -->
                <div class="p-6 md:p-8">
                    <form id="surveyForm" method="POST" action="<?= base_url('survey/kepuasan/submit') ?>">
                        <!-- PROFIL RESPONDEN -->
                        <div class="mb-8">
                            <h2 class="text-xl font-bold text-gray-800 border-b-2 border-primary pb-2 mb-6">PROFIL RESPONDEN</h2>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Jenis Kelamin <span class="text-red-500">*</span></label>
                                    <div class="flex gap-4">
                                        <label class="flex items-center">
                                            <input type="radio" name="jenis_kelamin" value="L" required class="mr-2 text-primary focus:ring-primary">
                                            <span>Laki-laki</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="radio" name="jenis_kelamin" value="P" required class="mr-2 text-primary focus:ring-primary">
                                            <span>Perempuan</span>
                                        </label>
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="pendidikan" class="block text-sm font-medium text-gray-700 mb-2">Pendidikan <span class="text-red-500">*</span></label>
                                    <select id="pendidikan" name="pendidikan" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary">
                                        <option value="">Pilih Pendidikan</option>
                                        <option value="SD">SD</option>
                                        <option value="SMP">SMP</option>
                                        <option value="SMA">SMA</option>
                                        <option value="S1">S1</option>
                                        <option value="S2">S2</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="usia" class="block text-sm font-medium text-gray-700 mb-2">Usia <span class="text-red-500">*</span></label>
                                    <div class="flex">
                                        <input type="number" id="usia" name="usia" min="17" max="100" required class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-primary focus:border-primary">
                                        <span class="px-3 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-lg text-gray-600">tahun</span>
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="pekerjaan" class="block text-sm font-medium text-gray-700 mb-2">Pekerjaan <span class="text-red-500">*</span></label>
                                    <select id="pekerjaan" name="pekerjaan" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary">
                                        <option value="">Pilih Pekerjaan</option>
                                        <option value="PNS">PNS</option>
                                        <option value="TNI">TNI</option>
                                        <option value="POLRI">POLRI</option>
                                        <option value="SWASTA">Swasta</option>
                                        <option value="WIRAUSAHA">Wirausaha</option>
                                        <option value="LAINNYA">Lainnya</option>
                                    </select>
                                </div>
                                
                                <div id="pekerjaan_lainnya_group" class="hidden">
                                    <label for="pekerjaan_lainnya" class="block text-sm font-medium text-gray-700 mb-2">Sebutkan Pekerjaan Lainnya</label>
                                    <input type="text" id="pekerjaan_lainnya" name="pekerjaan_lainnya" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary">
                                </div>
                                
                                <div>
                                    <label for="jenis_layanan" class="block text-sm font-medium text-gray-700 mb-2">Jenis Layanan yang Diterima <span class="text-red-500">*</span></label>
                                    <textarea id="jenis_layanan" name="jenis_layanan" rows="2" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" placeholder="Contoh: Surat Keterangan Domisili"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- PENDAPAT RESPONDEN TENTANG PELAYANAN -->
                        <div class="mb-8">
                            <h2 class="text-xl font-bold text-gray-800 border-b-2 border-primary pb-2 mb-6">PENDAPAT RESPONDEN TENTANG PELAYANAN</h2>
                            <p class="text-gray-600 text-sm mb-6">Pilih jawaban sesuai dengan pendapat Anda</p>
                            
                            <?php 
                            $questions = array(
                                'u1' => array(
                                    'title' => 'Bagaimana pendapat Saudara tentang kesesuaian persyaratan pelayanan dengan jenis pelayanannya?',
                                    'options' => array('Tidak sesuai', 'Kurang sesuai', 'Sesuai', 'Sangat sesuai')
                                ),
                                'u2' => array(
                                    'title' => 'Bagaimana pemahaman Saudara tentang kemudahan prosedur pelayanan di unit ini?',
                                    'options' => array('Tidak mudah', 'Kurang mudah', 'Mudah', 'Sangat mudah')
                                ),
                                'u3' => array(
                                    'title' => 'Bagaimana pendapat Saudara tentang kecepatan waktu dalam memberikan pelayanan?',
                                    'options' => array('Tidak cepat', 'Kurang cepat', 'Cepat', 'Sangat cepat')
                                ),
                                'u4' => array(
                                    'title' => 'Bagaimana pendapat Saudara tentang kewajaran biaya/tarif dalam pelayanan?',
                                    'options' => array('Sangat mahal', 'Cukup mahal', 'Murah', 'Gratis')
                                ),
                                'u5' => array(
                                    'title' => 'Bagaimana pendapat Saudara tentang kesesuaian produk pelayanan antara yang tercantum dalam standar pelayanan dengan hasil yang diberikan?',
                                    'options' => array('Tidak sesuai', 'Kurang sesuai', 'Sesuai', 'Sangat sesuai')
                                ),
                                'u6' => array(
                                    'title' => 'Bagaimana pendapat Saudara tentang kompetensi/kemampuan petugas dalam pelayanan?',
                                    'options' => array('Tidak kompeten', 'Kurang kompeten', 'Kompeten', 'Sangat kompeten')
                                ),
                                'u7' => array(
                                    'title' => 'Bagaimana pendapat Saudara tentang perilaku petugas dalam pelayanan terkait kesopanan dan keramahan?',
                                    'options' => array('Tidak sopan dan ramah', 'Kurang sopan dan ramah', 'Sopan dan ramah', 'Sangat sopan dan ramah')
                                ),
                                'u8' => array(
                                    'title' => 'Bagaimana pendapat Saudara tentang kualitas sarana dan prasarana?',
                                    'options' => array('Buruk', 'Cukup', 'Baik', 'Sangat baik')
                                ),
                                'u9' => array(
                                    'title' => 'Bagaimana pendapat Saudara tentang penanganan pengaduan pengguna layanan?',
                                    'options' => array('Tidak ada', 'Ada tetapi tidak berfungsi', 'Berfungsi kurang maksimal', 'Dikelola dengan baik')
                                )
                            );
                            
                            $counter = 1;
                            foreach ($questions as $key => $question): ?>
                                <div class="mb-6 p-4 bg-gray-50 rounded-lg border">
                                    <h3 class="font-semibold text-gray-800 mb-4"><?= $counter ?>. <?= $question['title'] ?></h3>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                        <?php foreach ($question['options'] as $index => $option): ?>
                                            <label class="flex items-center p-2 hover:bg-white rounded cursor-pointer transition-colors">
                                                <input type="radio" name="<?= $key ?>" value="<?= $index + 1 ?>" required class="mr-3 text-primary focus:ring-primary">
                                                <span class="text-sm"><?= chr(97 + $index) ?>. <?= $option ?></span>
                                            </label>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php 
                                $counter++;
                            endforeach; ?>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="bg-primary hover:bg-primary-dark text-white font-bold py-3 px-8 rounded-lg transition duration-300 transform hover:scale-105">
                                <i class="fas fa-paper-plane mr-2"></i>Kirim Survey
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide pekerjaan lainnya field
    const pekerjaanSelect = document.getElementById('pekerjaan');
    const pekerjaanLainnyaGroup = document.getElementById('pekerjaan_lainnya_group');
    const pekerjaanLainnyaInput = document.getElementById('pekerjaan_lainnya');
    
    pekerjaanSelect.addEventListener('change', function() {
        if (this.value === 'LAINNYA') {
            pekerjaanLainnyaGroup.classList.remove('hidden');
            pekerjaanLainnyaInput.required = true;
        } else {
            pekerjaanLainnyaGroup.classList.add('hidden');
            pekerjaanLainnyaInput.required = false;
            pekerjaanLainnyaInput.value = '';
        }
    });

    // Handle form submission
    $('#surveyForm').on('submit', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: $(this).attr('action'),
            method: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            beforeSend: function() {
                $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Mengirim...');
            },
            success: function(response) {
                if (response.RESULT === 'OK') {
                    swal('Berhasil!', response.MESSAGE, 'success').then(function() {
                        window.location.href = '<?= base_url() ?>';
                    });
                } else {
                    swal('Gagal!', response.MESSAGE, 'error');
                }
            },
            error: function() {
                swal('Error!', 'Terjadi kesalahan sistem. Silakan coba lagi.', 'error');
            },
            complete: function() {
                $('button[type="submit"]').prop('disabled', false).html('<i class="fas fa-paper-plane mr-2"></i>Kirim Survey');
            }
        });
    });
});
</script>
