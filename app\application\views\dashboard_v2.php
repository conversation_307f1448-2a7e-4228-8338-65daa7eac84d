<?php
defined('BASEPATH') or exit('No direct script access allowed!');
?>
<style>
    .card-news {
        display: flex;
        overflow: hidden;
    }

    .card-news .news-item {
        width: 250px;
        margin-right: 10px;
    }

    .card-news .news-item img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        object-position: center;
        border-radius: 10px;
    }

    .card-news .news-item .card .card-body {
        padding: .25rem;
    }

    .card-news .news-item .card .card-body .news-detail {
        padding: .5rem;
    }

    .news-detail h6 {
        color: #11142D;
        font-weight: 700;
        font-size: 16px;
        margin-bottom: 10px;
    }

    .news-detail p {
        font-size: 12px;
        color: #525571;
        margin-bottom: 0;
        text-align: justify;
    }

    .news-detail a {
        display: inline-block;
        text-decoration: none;
        color: #2AC16B;
        font-size: 14px;
        margin-top: .5rem;
    }

    /* Desil Chart Styles */
    .desil-legend {
        max-height: 400px;
        overflow-y: auto;
    }

    .desil-item {
        display: flex;
        align-items: center;
        font-size: 12px;
        line-height: 1.4;
    }

    .desil-color {
        width: 16px;
        height: 16px;
        border-radius: 3px;
        margin-right: 8px;
        flex-shrink: 0;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .desil-characteristics {
        font-size: 11px;
        color: #666;
        margin-top: 2px;
        padding-left: 24px;
    }

    #map {
        height: 400px;
        width: 100%;
    }
</style>

<?php if (isAdmin() && getCurrentUser()->email == null) : ?>
    <div class="alert alert-info">
        <h4 class="alert-heading h5 mb-1">Informasi Update Keamanan!</h4>
        <p class="mb-0">Anda belum memperbarui email akun anda. Silahkan perbarui email akun anda untuk memudahkan proses lupa password. <a href="<?= base_url('changeprofile') ?>" class="text-decoration-none">Klik disini</a> untuk memperbarui alamat email anda.</p>
    </div>
<?php endif; ?>

<?php if (isset($error_message) && $error_message) : ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <h4 class="alert-heading h5 mb-1"><i class="fas fa-exclamation-triangle"></i> Terjadi Kesalahan!</h4>
        <p class="mb-0"><?= htmlspecialchars($error_message) ?></p>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
<?php endif; ?>

<?php if (isSuperAdmin() || isAdmin() || isDiskominfo() || isPMD() || isKecamatan()) : ?>
    <div class="row mb-2">
        <div class="col-md-3">
            <div class="card mb-3">
                <div class="card-body">
                    <p><i class="fa fa-book mr-2 box box-cyan"></i>Jumlah Berita</p>
                    <h3><?= $berita ?></h3>
                    <small>Total</small>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card mb-3">
                <div class="card-body">
                    <p><i class="fa fa-tree mr-2 box box-orange"></i>Jumlah Wisata</p>
                    <h3><?= $wisata ?></h3>
                    <small>Total</small>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card mb-3">
                <div class="card-body">
                    <p><i class="fa fa-shopping-bag mr-2 box box-blue"></i>Produk</p>
                    <h3><?= $produk ?></h3>
                    <small>Total</small>
                </div>
            </div>
        </div>

        <?php if (isAdmin()) : ?>
            <div class="col-md-3">
                <div class="card mb-3">
                    <div class="card-body">
                        <p><i class="fa fa-cog mr-2 box box-yellow"></i>Pengaturan Umum</p>
                        <h3><?= $persentase ?>%</h3>
                        <small>Pengaturan Terisi</small>
                    </div>
                </div>
            </div>
        <?php else : ?>
            <div class="col-md-3">
                <a href="javascript:;" class="text-decoration-none" style="color: unset;" onclick="desa()">
                    <div class="card mb-3">
                        <div class="card-body">
                            <p><i class="fa fa-tree mr-2 box box-yellow"></i>Total Desa/Kelurahan</p>
                            <h3><?= $totaldesa ?></h3>
                            <small>Total</small>
                        </div>
                    </div>
                </a>
            </div>
        <?php endif; ?>
    </div>

    <div class="row mb-2">
        <?php if (isAdmin() || isOperatorDesa()) : ?>
            <div class="col-md-3">
                <div class="card mb-3">
                    <div class="card-body">
                        <p><i class="fa fa-dollar-sign mr-2 box box-cyan"></i>Penghasilan</p>
                        <h3>Rp <?= IDR((getCurrentUser()->royalty ?? 0)) ?></h3>
                        <small>Saat ini</small>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <div class="col-3">
            <a href="javascript:;" class="text-decoration-none" onclick="daily()">
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="traffic">
                            <div>
                                <i class="fa fa-chart-bar box box-cyan-soft box-large d-flex justify-content-center align-items-center" style="color: #18BAB1;"></i>
                            </div>

                            <div>
                                <p class="mb-0">Total <br> Pengunjung Hari Ini</p>
                            </div>
                        </div>

                        <div class="traffic-count">
                            <h3><?= $visit_today ?? 0 ?></h3>
                        </div>
                    </div>
                </div>
            </a>
        </div>

        <div class="col-3">
            <a href="javascript:;" onclick="monthly()" class="text-decoration-none">
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="traffic">
                            <div>
                                <i class="fa fa-chart-bar box box-orange box-large d-flex justify-content-center align-items-center" style="color: #A3430A;"></i>
                            </div>

                            <div>
                                <p class="mb-0">Total <br> Pengunjung Bulan Ini</p>
                            </div>
                        </div>

                        <div class="traffic-count">
                            <h3><?= $visit_month ?? 0 ?></h3>
                        </div>
                    </div>
                </div>
            </a>
        </div>

        <div class="col-3">
            <a href="javascript:;" onclick="yearly()" class="text-decoration-none">
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="traffic">
                            <div>
                                <i class="fa fa-chart-bar box box-red box-large d-flex justify-content-center align-items-center"></i>
                            </div>

                            <div>
                                <p class="mb-0">Total <br> Pengunjung Tahun Ini</p>
                            </div>
                        </div>

                        <div class="traffic-count">
                            <h3><?= $visit_year ?? 0 ?></h3>
                        </div>
                    </div>
                </div>
            </a>
        </div>
    </div>


    <?php if (isSuperAdmin() || isDiskominfo() || (isPMD() && count($kabupatendesa) > 0)) : ?>
        <form action="<?= base_url(uri_string()) ?>" method="GET" autocomplete="off">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="">Kabupaten</label>
                        <select name="kabupaten" class="form-control" onchange="kabupatenChange(this)">
                            <option value="">- Semua -</option>
                            <?php foreach ($kabupatendesa as $key => $value) : ?>
                                <option value="<?= $value->id_kabkota ?>" <?= $selectedkabupaten == $value->id_kabkota ? 'selected' : null ?>><?= $value->nama_kabkota ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group">
                        <label for="">Kecamatan</label>
                        <select name="kecamatan" class="form-control" onchange="kecamatanChange(this)">
                            <option value="">- Semua -</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group">
                        <label for="">Desa/Kelurahan</label>
                        <select name="desa" class="form-control">
                            <option value="">- Semua -</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-1">
                    <div class="form-group">
                        <label for="">Aksi</label>

                        <div>
                            <button type="submit" class="btn btn-primary">Filter</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="clearfix"></div>
        </form>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-8">
            <div class="row">
                <div class="col-12">
                    <?php if (isAdmin() && $coords != null) : ?>
                        <div class="card mb-3">
                            <div class="card-body">
                                <div id="map"></div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="card mb-3">
                        <div class="card-body">
                            <ul class="nav nav-tabs" id="myTab" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" id="daily-tab" data-toggle="tab" href="#daily" role="tab" aria-controls="daily" aria-selected="true">Hari</a>
                                </li>

                                <li class="nav-item">
                                    <a class="nav-link" id="monthly-tab" data-toggle="tab" href="#monthly" role="tab" aria-controls="monthly" aria-selected="false">Bulan</a>
                                </li>
                            </ul>

                            <div class="tab-content" id="myTabContent">
                                <div class="tab-pane fade show active" id="daily" role="tabpanel" aria-labelledby="daily-tab">
                                    <div id="bar-chart-1"></div>
                                </div>

                                <div class="tab-pane fade" id="monthly" role="tabpanel" aria-labelledby="monthly-tab">
                                    <div id="bar-chart-2"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="row">
                <div class="col-12">
                    <div class="card mb-3">
                        <div class="card-body">
                            <canvas id="barChart" width="400" height="400"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-3">
                <div class="card-body">
                    <h6>Infografis Pendidikan</h6>

                    <canvas id="myChart" width="400" height="400"></canvas>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-3">
                <div class="card-body">
                    <h6>Infografis Pekerjaan</h6>

                    <canvas id="myChartWork" width="400" height="400"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Desil Chart Section -->
    <?php if (isAdmin() || isOperatorDesa()) : ?>
        <div class="row">
            <div class="col-md-8">
                <div class="card mb-3">
                    <div class="card-body">
                        <h6>Distribusi Desil Warga</h6>
                        <p class="text-muted small mb-3">Distribusi tingkat ekonomi warga berdasarkan klasifikasi desil (1-10)</p>
                        <canvas id="desilChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-3">
                    <div class="card-body">
                        <h6>Penjelasan Desil</h6>
                        <div class="desil-legend">
                            <div class="desil-item mb-3">
                                <span class="desil-color" style="background-color: rgba(220, 53, 69, 0.8);"></span>
                                <div>
                                    <strong>Desil 1:</strong> Paling Miskin
                                    <div class="desil-characteristics">Tidak memiliki rumah sendiri, kesulitan memenuhi kebutuhan dasar, tidak ada akses layanan kesehatan</div>
                                </div>
                            </div>
                            <div class="desil-item mb-3">
                                <span class="desil-color" style="background-color: rgba(255, 99, 71, 0.8);"></span>
                                <div>
                                    <strong>Desil 2:</strong> Miskin
                                    <div class="desil-characteristics">Rumah tidak layak huni, pendapatan tidak tetap, akses terbatas ke pendidikan dan kesehatan</div>
                                </div>
                            </div>
                            <div class="desil-item mb-3">
                                <span class="desil-color" style="background-color: rgba(255, 140, 0, 0.8);"></span>
                                <div>
                                    <strong>Desil 3:</strong> Miskin Bawah
                                    <div class="desil-characteristics">Rumah sederhana, pekerjaan serabutan, anak bersekolah dasar, akses kesehatan terbatas</div>
                                </div>
                            </div>
                            <div class="desil-item mb-3">
                                <span class="desil-color" style="background-color: rgba(255, 165, 0, 0.8);"></span>
                                <div>
                                    <strong>Desil 4:</strong> Miskin Menengah / Rentan Miskin
                                    <div class="desil-characteristics">Rumah layak, pekerjaan tidak tetap, rentan jatuh miskin saat krisis ekonomi</div>
                                </div>
                            </div>
                            <div class="desil-item mb-3">
                                <span class="desil-color" style="background-color: rgba(255, 215, 0, 0.8);"></span>
                                <div>
                                    <strong>Desil 5:</strong> Menengah Bawah
                                    <div class="desil-characteristics">Rumah permanen sederhana, pekerjaan tetap dengan gaji minimal, akses pendidikan menengah</div>
                                </div>
                            </div>
                            <div class="desil-item mb-3">
                                <span class="desil-color" style="background-color: rgba(173, 255, 47, 0.8);"></span>
                                <div>
                                    <strong>Desil 6:</strong> Menengah Stabil
                                    <div class="desil-characteristics">Rumah layak dengan fasilitas lengkap, pekerjaan tetap, mampu menabung sedikit</div>
                                </div>
                            </div>
                            <div class="desil-item mb-3">
                                <span class="desil-color" style="background-color: rgba(124, 252, 0, 0.8);"></span>
                                <div>
                                    <strong>Desil 7:</strong> Menengah Atas
                                    <div class="desil-characteristics">Rumah bagus, kendaraan pribadi, anak kuliah, akses layanan kesehatan swasta</div>
                                </div>
                            </div>
                            <div class="desil-item mb-3">
                                <span class="desil-color" style="background-color: rgba(50, 205, 50, 0.8);"></span>
                                <div>
                                    <strong>Desil 8:</strong> Kelas Menengah Mapan
                                    <div class="desil-characteristics">Rumah mewah, investasi properti, pendidikan tinggi, asuransi kesehatan premium</div>
                                </div>
                            </div>
                            <div class="desil-item mb-3">
                                <span class="desil-color" style="background-color: rgba(34, 139, 34, 0.8);"></span>
                                <div>
                                    <strong>Desil 9:</strong> Kaya
                                    <div class="desil-characteristics">Multiple properti, usaha sendiri, investasi saham/obligasi, gaya hidup mewah</div>
                                </div>
                            </div>
                            <div class="desil-item mb-3">
                                <span class="desil-color" style="background-color: rgba(0, 100, 0, 0.8);"></span>
                                <div>
                                    <strong>Desil 10:</strong> Paling Kaya
                                    <div class="desil-characteristics">Aset berlimpah, bisnis besar, investasi diversifikasi, pengaruh sosial tinggi</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <div class="row">
        <?php if (!isAdmin()) : ?>
            <div class="col-md-12">
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6>Performa Desa</h6>
                        </div>

                        <div class="table-responsive mt-4">
                            <table class="table table-bordered table-striped table-hover text-nowrap datatables">
                                <thead>
                                    <tr>
                                        <th>Desa/Kelurahan</th>
                                        <th>Pengaturan Umum</th>
                                        <th>Kontak Penting</th>
                                        <th>Infografis</th>
                                        <th>Anggaran</th>
                                        <th>Warga</th>
                                        <th>Aset</th>
                                        <th>Struktur Organisasi</th>
                                        <th>Berita</th>
                                        <th>Pengunjung Berita</th>
                                        <th>Produk</th>
                                        <th>Produk Hukum</th>
                                        <th>Wisata</th>
                                        <th>Slider</th>
                                        <th>Highlight</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <?php foreach ($performance as $key => $value) : ?>
                                        <tr>
                                            <td>
                                                <a href="https://<?= $value->subdomain ?>.<?= ($value->platformname != 'GidesManis' ? 'gides.id' : 'gidesmanis.id') ?>/" target="_blank"><?= $value->nama_kelurahan ?></a>
                                            </td>
                                            <td>
                                                <?php if ($value->setting_umum >= 1) : ?>
                                                    <i class="fas fa-check-circle text-success"></i>
                                                <?php else : ?>
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($value->kontak_penting >= 1) : ?>
                                                    <i class="fas fa-check-circle text-success"></i>

                                                    <small><?= IDR($value->kontak_penting) ?></small>
                                                <?php else : ?>
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($value->infografis >= 1) : ?>
                                                    <i class="fas fa-check-circle text-success"></i>
                                                <?php else : ?>
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($value->apbdesa >= 1) : ?>
                                                    <i class="fas fa-check-circle text-success"></i>

                                                    <small><?= IDR($value->apbdesa) ?></small>
                                                <?php else : ?>
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($value->warga >= 1) : ?>
                                                    <i class="fas fa-check-circle text-success"></i>

                                                    <small><?= IDR($value->warga) ?></small>
                                                <?php else : ?>
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($value->asset >= 1) : ?>
                                                    <i class="fas fa-check-circle text-success"></i>

                                                    <small><?= IDR($value->asset) ?></small>
                                                <?php else : ?>
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($value->strukturorganisasi >= 1) : ?>
                                                    <i class="fas fa-check-circle text-success"></i>

                                                    <small><?= IDR($value->strukturorganisasi) ?></small>
                                                <?php else : ?>
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($value->berita >= 1) : ?>
                                                    <i class="fas fa-check-circle text-success"></i>

                                                    <small><?= IDR($value->berita) ?></small>
                                                <?php else : ?>
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($value->pengunjungberita >= 1) : ?>
                                                    <i class="fas fa-check-circle text-success"></i>

                                                    <small><?= IDR($value->pengunjungberita) ?></small>
                                                <?php else : ?>
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($value->produk >= 1) : ?>
                                                    <i class="fas fa-check-circle text-success"></i>

                                                    <small><?= IDR($value->produk) ?></small>
                                                <?php else : ?>
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($value->produkhukum >= 1) : ?>
                                                    <i class="fas fa-check-circle text-success"></i>

                                                    <small><?= IDR($value->produkhukum) ?></small>
                                                <?php else : ?>
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($value->wisata >= 1) : ?>
                                                    <i class="fas fa-check-circle text-success"></i>

                                                    <small><?= IDR($value->wisata) ?></small>
                                                <?php else : ?>
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($value->slider >= 1) : ?>
                                                    <i class="fas fa-check-circle text-success"></i>

                                                    <small><?= IDR($value->slider) ?></small>
                                                <?php else : ?>
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($value->highlight >= 1) : ?>
                                                    <i class="fas fa-check-circle text-success"></i>

                                                    <small><?= IDR($value->highlight) ?></small>
                                                <?php else : ?>
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12">
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6>Peringkat Desa/Kelurahan se-Kabupaten</h6>

                            <?php if (isPMD()) : ?>
                                <a href="<?= base_url('admin/pemerintah/download/kabupaten') ?>" class="btn btn-primary btn-sm" target="_blank">
                                    <i class="fa fa-download"></i>
                                    <span>Download Seluruh Perangkat</span>
                                </a>
                            <?php endif; ?>
                        </div>

                        <div class="table-responsive mt-4">
                            <table class="table table-bordered table-striped table-hover text-nowrap datatables">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Desa/Kelurahan</th>
                                        <th>Jumlah Perangkat</th>
                                        <th>Jumlah Pengunjung Bulanan</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <?php foreach ($rank_month as $key => $value) : ?>
                                        <tr>
                                            <td><?= $key + 1 ?></td>
                                            <td><?= $value->nama_kelurahan ?></td>
                                            <td><?= IDR($value->perangkatdesa) ?></td>
                                            <td><?= IDR($value->total) ?></td>
                                            <td>
                                                <a href="javascript:;" class="btn btn-success btn-sm" onclick="perangkatDesa(<?= $value->id ?>)">
                                                    <i class="fa fa-users"></i>
                                                </a>

                                                <a href="<?= base_url('strukturorganisasi/download?id=' . $value->id) ?>" target="_blank" class="btn btn-primary btn-sm">
                                                    <i class="fa fa-download"></i>
                                                </a>

                                                <a href="https://<?= $value->subdomain ?>.<?= $value->platformname == 'Gides' || $value->platformname == null ? 'gides.id' : 'gidesmanis.id' ?>" target="_blank" class="btn btn-warning btn-sm">
                                                    <i class="fa fa-external-link"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (isset($kecamatan)) : ?>
                <?php foreach ($kecamatan as $key => $value) : ?>
                    <?php
                    $get = $trafficreport->getRankMonth(array('c.id_kecamatan' => $value->id_kecamatan, 'MONTH(b.createddate) =' => date('m'), 'YEAR(b.createddate) =' => date('Y')), array());

                    if (count($get) == 0) continue;
                    ?>

                    <div class="col-6">
                        <div class="card mb-3">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6>Peringkat Desa dari se-Kecamatan <?= $value->nama_kecamatan ?></h6>

                                    <?php if (isPMD()) : ?>
                                        <a href="<?= base_url('admin/pemerintah/download/kecamatan/' . $value->id_kecamatan) ?>" class="btn btn-primary btn-sm" target="_blank">
                                            <i class="fa fa-download"></i>
                                            <span>Download Seluruh Perangkat</span>
                                        </a>
                                    <?php endif; ?>
                                </div>

                                <div class="table-responsive mt-4">
                                    <table class="table table-bordered table-striped table-hover text-nowrap datatables">
                                        <thead>
                                            <tr>
                                                <th>No</th>
                                                <th>Desa</th>
                                                <th>Total Perangkat Desa</th>
                                                <th>Total Pengunjung Bulanan</th>
                                                <th>Aksi</th>
                                            </tr>
                                        </thead>

                                        <tbody>
                                            <?php foreach ($get as $key => $value) : ?>
                                                <tr>
                                                    <td><?= $key + 1 ?></td>
                                                    <td><?= $value->nama_kelurahan ?></td>
                                                    <td><?= IDR($value->perangkatdesa) ?></td>
                                                    <td><?= IDR($value->total) ?></td>
                                                    <td>
                                                        <a href="https://<?= $value->subdomain ?>.<?= $value->platformname == 'Gides' || $value->platformname == null ? 'gides.id' : 'gidesmanis.id' ?>/pemerintah/download" target="_blank" class="btn btn-primary btn-sm">
                                                            <i class="fa fa-download"></i>
                                                        </a>

                                                        <a href="https://<?= $value->subdomain ?>.<?= $value->platformname == 'Gides' || $value->platformname == null ? 'gides.id' : 'gidesmanis.id' ?>" target="_blank" class="btn btn-warning btn-sm">
                                                            <i class="fa fa-external-link"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <div class="modal fade" id="addBerita" tabindex="-1" role="dialog" aria-labelledby="addBerita" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Tambah Berita</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <form id="frmAddBerita" action="<?= base_url('berita/add/process') ?>" method="POST" autocomplete="off">
                    <div class="modal-body">
                        <div class="form-group">
                            <label>Judul</label>
                            <input type="text" name="judul" class="form-control" placeholder="Judul" required>
                        </div>

                        <div class="form-group">
                            <label>Foto</label>
                            <input type="file" name="foto" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label>Deskripsi</label>
                            <textarea name="deskripsi" id="deskripsi" cols="10" rows="10" required></textarea>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php endif; ?>

<script>
    function deleteBerita(id) {
        return swal({
            title: 'Apakah anda yakin?',
            text: 'Apakah anda yakin ingin menghapus data ini?',
            icon: 'warning',
            buttons: true,
            dangerMode: true
        }).then(ok => {
            if (ok) {
                $.ajax({
                    url: '<?= base_url('berita/delete') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return swalMessageSuccess(response.MESSAGE, ok => {
                                window.location.reload();
                            });
                        } else {
                            return swalMessageFailed(response.MESSAGE);
                        }
                    }
                }).fail(function() {
                    return swalError();
                });
            }
        });
    }

    function tambah_berita() {
        CKEDITOR.replace('deskripsi');
        $('#addBerita').modal('show');
    }

    window.onload = function() {
        kabupatenChange('select[name=kabupaten]');
        kecamatanChange('select[name=kecamatan]');

        const ctx = document.getElementById('myChart');
        const myChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: <?= $infografis ?>,
                datasets: [{
                    label: 'Total',
                    data: <?= $datainfografis ?>,
                    backgroundColor: <?= $backgroundinfografis ?>,
                    borderColor: <?= $borderinfografis ?>,
                    borderWidth: 1
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true
            }
        });

        const ctx2 = document.getElementById('myChartWork');
        const myChart2 = new Chart(ctx2, {
            type: 'bar',
            data: {
                labels: <?= $infografiswork ?>,
                datasets: [{
                    label: 'Total',
                    data: <?= $datainfografiswork ?>,
                    backgroundColor: <?= $backgroundinfografiswork ?>,
                    borderColor: <?= $borderinfografiswork ?>,
                    borderWidth: 1
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true
            }
        });

        const bar = document.getElementById('barChart').getContext('2d');
        const barChart = new Chart(bar, {
            type: 'pie',
            data: {
                labels: ['Perempuan', 'Laki Laki'],
                datasets: [{
                    data: [<?= $perempuan ?>, <?= $lakilaki ?>],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.2)',
                        'rgba(54, 162, 235, 0.2)',
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'Persentase Infografis Jenis Kelamin'
                    }
                }
            },
        });

        // Desil Chart
        <?php if (isAdmin() || isOperatorDesa()) : ?>
            const desilCtx = document.getElementById('desilChart');
            if (desilCtx) {
                const desilChart = new Chart(desilCtx, {
                    type: 'bar',
                    data: {
                        labels: <?= $desil_labels ?>,
                        datasets: [{
                            label: 'Jumlah Warga',
                            data: <?= $desil_counts ?>,
                            backgroundColor: <?= $desil_colors ?>,
                            borderColor: <?= $desil_colors ?>,
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Distribusi Ekonomi Warga Berdasarkan Desil',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    title: function(context) {
                                        const desilNames = [
                                            'Desil 1 - Paling Miskin',
                                            'Desil 2 - Miskin',
                                            'Desil 3 - Miskin Bawah',
                                            'Desil 4 - Miskin Menengah / Rentan Miskin',
                                            'Desil 5 - Menengah Bawah',
                                            'Desil 6 - Menengah Stabil',
                                            'Desil 7 - Menengah Atas',
                                            'Desil 8 - Kelas Menengah Mapan',
                                            'Desil 9 - Kaya',
                                            'Desil 10 - Paling Kaya'
                                        ];
                                        return desilNames[context[0].dataIndex];
                                    },
                                    label: function(context) {
                                        return 'Jumlah Warga: ' + context.parsed.y + ' orang';
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Jumlah Warga'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Tingkat Desil'
                                }
                            }
                        }
                    }
                });
            }
        <?php endif; ?>

        $('.card-news').slick({
            infinite: true,
            slidesToShow: 4,
            slidesToScroll: 1,
            arrows: false,
            autoplay: true,
            autoplaySpeed: 2000,
            // responsive: [{
            //         breakpoint: 992,
            //         settings: {
            //             slidesToShow: 3
            //         }
            //     },
            //     {
            //         breakpoint: 768,
            //         settings: {
            //             slidesToShow: 3
            //         }
            //     },
            //     {
            //         breakpoint: 576,
            //         settings: {
            //             slidesToShow: 1
            //         }
            //     },
            // ]
        });


        <?php if (isPMD()) : ?> $('.sidebar').addClass('hide');
            $('.content').addClass('full');
        <?php endif; ?>

        $('#frmAddBerita').submit(function(e) {
            e.preventDefault();

            let deskripsi = CKEDITOR.instances.deskripsi.getData();
            let formData = new FormData(this);
            formData.set('deskripsi', deskripsi);

            let elementsForm = $(this).find('button, textarea, input');

            elementsForm.attr('disabled', true);

            $.ajax({
                url: $(this).attr('action'),
                method: $(this).attr('method'),
                dataType: 'json',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    elementsForm.removeAttr('disabled');

                    if (response.RESULT == 'OK') {
                        return swalMessageSuccess(response.MESSAGE, ok => {
                            return window.location.reload();
                        });
                    } else {
                        return swalMessageFailed(response.MESSAGE);
                    }
                }
            }).fail(function() {
                elementsForm.removeAttr('disabled');

                return swalError();
            })
        });

        var daily = Morris.Bar({
            data: <?= $report_daily ?>,
            xkey: 'y',
            ykeys: ['a'],
            labels: ['Traffic'],
            resize: true,
            element: 'bar-chart-1',
            barColors: ['#18BAB1']
        });

        var monthly = Morris.Bar({
            data: <?= $report_monthly ?>,
            xkey: 'y',
            ykeys: ['a'],
            labels: ['Traffic'],
            resize: true,
            element: 'bar-chart-2',
            barColors: ['#18BAB1']
        });

        $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
            var target = $(e.target).attr("href") // activated tab

            switch (target) {
                case "#daily":
                    daily.redraw();
                    $(window).trigger('resize');
                    break;
                case "#monthly":
                    monthly.redraw();
                    $(window).trigger('resize');
                    break;
            }
        });

        setTimeout(function() {
            $(window).trigger('resize');
        }, 1000);
    };

    function daily() {
        $.ajax({
            url: '<?= base_url('traffic/report/daily') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                desa: '<?= $selecteddesa ?>'
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            }
        }).fail(function() {
            return swalError();
        });
    }

    function monthly() {
        $.ajax({
            url: '<?= base_url('traffic/report/monthly') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                desa: '<?= $selecteddesa ?>'
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            }
        }).fail(function() {
            return swalError();
        });
    }

    function yearly() {
        $.ajax({
            url: '<?= base_url('traffic/report/yearly') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                desa: '<?= $selecteddesa ?>'
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            }
        }).fail(function() {
            return swalError();
        });
    }

    function submitOnChange() {
        $('form').submit();
    }

    function kabupatenChange(these) {
        $.ajax({
            url: '<?= base_url('master/select/kecamatandesa') ?>',
            method: 'POST',
            dataType: 'html',
            data: {
                kabupatenid: $(these).val(),
                kecamatanid: '<?= $selectedkecamatan ?>'
            },
            success: function(response) {
                $('select[name=kecamatan]').html(response).change();
            }
        }).fail(function() {});
    }

    function kecamatanChange(these) {
        $('select[name=desa]').html(`<option value="">- Semua -</option>`);

        $.ajax({
            url: '<?= base_url('master/select/kelurahandesa') ?>',
            method: 'POST',
            dataType: 'html',
            data: {
                kecamatanid: $(these).val(),
                desaid: '<?= $selecteddesa ?>'
            },
            success: function(response) {
                $('select[name=desa]').html(response);
            }
        }).fail(function() {});
    }

    function desa() {
        $.ajax({
            url: '<?= base_url(uri_string() . '/desa') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                kecamatan: $('select[name=kecamatan]').val(),
                desa: '<?= $selecteddesa ?>'
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            }
        }).fail(function() {
            return swalError();
        });
    }

    function perangkatDesa(id) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/perangkat') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            }
        }).fail(function() {
            return swalError();
        })
    }
</script>

<audio id="alarm">
    <source src="<?= asset_url() ?>assets/mp3/alarm.mp3" type="audio/mpeg">
    Your browser does not support the audio element.
</audio>

<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBwylf4eX5wOPvQEZshnLCIz5M7u2VvgC4" async defer></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.6.2/socket.io.js" integrity="sha512-jMNwWSmjje4fjYut9MBGKXw5FZA6D67NHAuC9szpjbbjg51KefquNfvn4DalCbGfkcv/jHsHnPo1o47+8u4biA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="module">
    var socket = io("ws://**************:3000");

    // Import the functions you need from the SDKs you need
    import {
        initializeApp
    } from "https://www.gstatic.com/firebasejs/9.22.2/firebase-app.js";

    import {
        getMessaging,
        getToken,
        onMessage
    } from "https://www.gstatic.com/firebasejs/9.22.2/firebase-messaging.js";

    // https://firebase.google.com/docs/web/setup#available-libraries

    // Your web app's Firebase configuration
    // For Firebase JS SDK v7.20.0 and later, measurementId is optional
    const firebaseConfig = {
        apiKey: "AIzaSyBw5zd9iiya7Jrwk83iHiSjMYKB3nlPj_I",
        authDomain: "gides-257fd.firebaseapp.com",
        projectId: "gides-257fd",
        storageBucket: "gides-257fd.appspot.com",
        messagingSenderId: "597115232378",
        appId: "1:597115232378:web:b77616992a4c1c4aa21bc3"
    };

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const messaging = getMessaging(app);

    function initialize_getToken() {
        getToken(messaging, {
            vapidKey: 'BOznTE4h2NBn4vARTkSENjoSq4z3QFEB9hB2e6qZZsECT0Pcr_8dEtZAgs0BusqEQS8QC9zswbEPvFFGpVaSAE4'
        }).then((currentToken) => {
            if (currentToken) {
                socket.emit('token', {
                    token: currentToken
                });

                $.ajax({
                    url: '<?= base_url('fb/save_token') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        token: currentToken
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            console.log('Token saved.');
                        } else {
                            console.log('Token not saved.');
                        }
                    }
                }).fail(function() {
                    console.log('Token not saved.')
                });
            } else {
                console.log('No registration token available. Request permission to generate one.');
            }
        }).catch((err) => {
            console.log('An error occurred while retrieving token. ', err);
        });
    }

    function requestPermission() {
        Notification.requestPermission().then((permission) => {
            if (permission == 'granted') {
                initialize_getToken();
            } else {
                console.log(permission);
            }
        });
    }

    requestPermission();

    <?php if (isAdmin() && $coords != null) : ?>
        var map = new google.maps.Map(document.getElementById("map"), {
            zoom: 13,
            center: <?= $firstcoords ?>
        });

        var infowindow = new google.maps.InfoWindow();
        var boundaryCoords = <?= $coords ?>;

        var boundary = new google.maps.Polygon({
            paths: boundaryCoords,
            strokeColor: "#FF0000", // Warna stroke
            strokeOpacity: 0.8, // Opasitas stroke
            strokeWeight: 2 // Ketebalan stroke
        });

        boundary.setMap(map);

        socket.on('panic', (e) => {
            document.getElementById('alarm').play();

            var marker = new google.maps.Marker({
                position: new google.maps.LatLng(e.lat, e.lng),
                map: map,
                animation: google.maps.Animation.BOUNCE
            });

            google.maps.event.addListener(marker, 'click', function() {
                infowindow.setContent(`<div class="text-center"><p class="mb-2">Warga sedang mengalami ${e.type}</p><button type="button" class="btn btn-primary btn-sm">Buka Maps</button></div>`);
                infowindow.open(map, this);
            });
        });
    <?php endif; ?>
</script>