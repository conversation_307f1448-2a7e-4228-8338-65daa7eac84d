<?php

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property SKM_Survey $skm_survey
 * @property Setting_Umum $settingumum
 * @property Kontak_Penting $kontakpenting
 * @property CI_Input $input
 * @property CI_DB_mysqli_driver $db
 */
class Survey extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('SKM_Survey', 'skm_survey');
        $this->load->model('Perilaku_Survey', 'perilaku_survey');
        $this->load->model('Setting_Umum', 'settingumum');
        $this->load->model('Kontak_Penting', 'kontakpenting');
    }

    // Public survey form
    public function kepuasan()
    {
        // Check if subdomain_account exists, if not create dummy
        if (!isset($this->subdomain_account) || !$this->subdomain_account) {
            $this->subdomain_account = new stdClass();
            $this->subdomain_account->id = 1;
            $this->subdomain_account->themeid = 1;
        }

        $setting = $this->settingumum->getDefaultData(array('id_user' => $this->subdomain_account->id));
        $kontakpenting = $this->kontakpenting->getDefaultData(array('id_user' => $this->subdomain_account->id))->result();

        $data = array();
        $data['title'] = 'Survey Kepuasan Masyarakat';
        $data['setting'] = $setting->num_rows() > 0 ? $setting->row() : null;
        $data['kontakpenting'] = $kontakpenting;
        $data['user'] = $this->subdomain_account;

        // If no setting found, create dummy setting
        if (!$data['setting']) {
            $data['setting'] = new stdClass();
            $data['setting']->desa = 'Desa Survey';
            $data['setting']->nama_desa = 'Desa Survey';
        }

        // ProGides platform always uses ProGides theme regardless of theme setting
        if (function_exists('getPlatformName') && getPlatformName() == 'ProGides') {
            $data['content'] = 'progides/survey_form';
            return $this->load->view('progides/master', $data);
        } elseif ($this->subdomain_account->themeid == 2) {
            $data['content'] = 'profile/survey_form';
            return $this->load->view('profile/master', $data);
        } else {
            $data['content'] = 'landing/survey_form';
            return $this->load->view('landing/master', $data);
        }
    }

    // Public survey perilaku form (landing)
    public function perilaku()
    {
        if (!isset($this->subdomain_account) || !$this->subdomain_account) {
            $this->subdomain_account = new stdClass();
            $this->subdomain_account->id = 1;
            $this->subdomain_account->themeid = 1;
        }

        $setting = $this->settingumum->getDefaultData(array('id_user' => $this->subdomain_account->id));
        $kontakpenting = $this->kontakpenting->getDefaultData(array('id_user' => $this->subdomain_account->id))->result();

        $data = array();
        $data['title'] = 'Survey Perilaku';
        $data['setting'] = $setting->num_rows() > 0 ? $setting->row() : null;
        $data['kontakpenting'] = $kontakpenting;
        $data['user'] = $this->subdomain_account;

        if (!$data['setting']) {
            $data['setting'] = new stdClass();
            $data['setting']->desa = 'Desa Survey';
            $data['setting']->nama_desa = 'Desa Survey';
        }

        // Route to theme-specific views similar to SKM
        if (function_exists('getPlatformName') && getPlatformName() == 'ProGides') {
            $data['content'] = 'progides/survey_perilaku_form';
            return $this->load->view('progides/master', $data);
        } elseif ($this->subdomain_account->themeid == 2) {
            $data['content'] = 'profile/survey_perilaku_form';
            return $this->load->view('profile/master', $data);
        } else {
            $data['content'] = 'landing/survey_perilaku_form';
            return $this->load->view('landing/master', $data);
        }
    }


    // Test method
    public function test()
    {
        echo "Survey controller is working!";
    }

    // Submit survey form
    public function submit()
    {
        if ($this->input->method() !== 'post') {
            return redirect(base_url('survey/kepuasan'));
        }

        // Validate required fields
        $required_fields = array(
            'jenis_kelamin',
            'pendidikan',
            'usia',
            'pekerjaan',
            'jenis_layanan',
            'u1',
            'u2',
            'u3',
            'u4',
            'u5',
            'u6',
            'u7',
            'u8',
            'u9'
        );

        foreach ($required_fields as $field) {
            if (empty($this->input->post($field))) {
                return JSONResponse(array(
                    'RESULT' => 'FAILED',
                    'MESSAGE' => 'Semua field wajib diisi!'
                ));
            }
        }

        // Validate unsur values (1-4)
        for ($i = 1; $i <= 9; $i++) {
            $value = $this->input->post("u$i");
            if (!in_array($value, array('1', '2', '3', '4'))) {
                return JSONResponse(array(
                    'RESULT' => 'FAILED',
                    'MESSAGE' => "Nilai untuk unsur $i tidak valid!"
                ));
            }
        }

        // Validate age
        $usia = $this->input->post('usia');
        if (!is_numeric($usia) || $usia < 17 || $usia > 100) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Usia harus berupa angka antara 17-100 tahun!'
            ));
        }

        // Prepare data for insertion
        $data = array(
            'desa_user_id' => $this->subdomain_account->id,
            'jenis_kelamin' => $this->input->post('jenis_kelamin'),
            'pendidikan' => $this->input->post('pendidikan'),
            'usia' => (int)$this->input->post('usia'),
            'pekerjaan' => $this->input->post('pekerjaan'),
            'pekerjaan_lainnya' => $this->input->post('pekerjaan') === 'LAINNYA' ? $this->input->post('pekerjaan_lainnya') : null,
            'jenis_layanan' => $this->input->post('jenis_layanan'),
            'u1' => (int)$this->input->post('u1'),
            'u2' => (int)$this->input->post('u2'),
            'u3' => (int)$this->input->post('u3'),
            'u4' => (int)$this->input->post('u4'),
            'u5' => (int)$this->input->post('u5'),
            'u6' => (int)$this->input->post('u6'),
            'u7' => (int)$this->input->post('u7'),
            'u8' => (int)$this->input->post('u8'),
            'u9' => (int)$this->input->post('u9'),
            'created_at' => date('Y-m-d H:i:s')
        );

        // Insert survey data
        if ($this->skm_survey->insert($data)) {
            return JSONResponse(array(
                'RESULT' => 'OK',
                'MESSAGE' => 'Terima kasih! Survey Anda telah berhasil disimpan.'
            ));
        } else {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Terjadi kesalahan saat menyimpan data. Silakan coba lagi.'
            ));
        }
    }


    // Submit survey perilaku
    public function submit_perilaku()
    {
        if ($this->input->method() !== 'post') {
            return redirect(base_url('survey/perilaku'));
        }

        $required_fields = array('jenis_kelamin', 'pendidikan', 'usia', 'pekerjaan');
        for ($i = 1; $i <= 10; $i++) {
            $required_fields[] = "u$i";
        }

        foreach ($required_fields as $field) {
            if (empty($this->input->post($field))) {
                return JSONResponse(array('RESULT' => 'FAILED', 'MESSAGE' => 'Semua field wajib diisi!'));
            }
        }

        for ($i = 1; $i <= 10; $i++) {
            $value = $this->input->post("u$i");
            if (!in_array($value, array('1', '2', '3', '4'))) {
                return JSONResponse(array('RESULT' => 'FAILED', 'MESSAGE' => "Nilai untuk unsur $i tidak valid!"));
            }
        }

        $usia = $this->input->post('usia');
        if (!is_numeric($usia) || $usia < 17 || $usia > 100) {
            return JSONResponse(array('RESULT' => 'FAILED', 'MESSAGE' => 'Usia harus berupa angka antara 17-100 tahun!'));
        }

        $data = array(
            'desa_user_id' => $this->subdomain_account->id,
            'jenis_kelamin' => $this->input->post('jenis_kelamin'),
            'pendidikan' => $this->input->post('pendidikan'),
            'usia' => (int)$this->input->post('usia'),
            'pekerjaan' => $this->input->post('pekerjaan'),
            'pekerjaan_lainnya' => $this->input->post('pekerjaan') === 'LAINNYA' ? $this->input->post('pekerjaan_lainnya') : null,
            'created_at' => date('Y-m-d H:i:s')
        );
        for ($i = 1; $i <= 10; $i++) {
            $data["u$i"] = (int)$this->input->post("u$i");
        }

        if ($this->perilaku_survey->insert($data)) {
            return JSONResponse(array('RESULT' => 'OK', 'MESSAGE' => 'Terima kasih! Survey Anda telah berhasil disimpan.'));
        } else {
            return JSONResponse(array('RESULT' => 'FAILED', 'MESSAGE' => 'Terjadi kesalahan saat menyimpan data. Silakan coba lagi.'));
        }
    }


    // Admin survey report
    public function admin_index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');

        $statistics = $this->skm_survey->get_survey_statistics(
            getCurrentIdUser(),
            $start_date,
            $end_date
        );

        $data = array();
        $data['title'] = 'Laporan Survey Kepuasan Masyarakat';
        $data['content'] = 'survey/index';
        $data['statistics'] = $statistics;
        $data['unsur_labels'] = $this->skm_survey->get_unsur_labels();
        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;

        return $this->load->view('master', $data);
    }

    // Export Excel (XLSX)
    public function export_csv()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');

        $this->db->where('desa_user_id', getCurrentIdUser());
        if ($start_date) {
            $this->db->where('created_at >=', $start_date . ' 00:00:00');
        }
        if ($end_date) {
            $this->db->where('created_at <=', $end_date . ' 23:59:59');
        }

        $surveys = $this->db->get('skm_survey')->result();
        if (empty($surveys)) {
            if (isset($this->session)) {
                $this->session->set_flashdata('error', 'Tidak ada data untuk diekspor');
            }
            return redirect('admin/survey');
        }

        // Ringkasan statistik
        $statistics = $this->skm_survey->get_survey_statistics(getCurrentIdUser(), $start_date, $end_date);
        $unsur_labels = $this->skm_survey->get_unsur_labels();

        // Helper kategori mutu per unsur
        $kategoriMutu = function ($ikm) {
            if ($ikm >= 81.26) return 'A (Sangat Baik)';
            if ($ikm >= 62.51) return 'B (Baik)';
            if ($ikm >= 43.76) return 'C (Kurang Baik)';
            return 'D (Tidak Baik)';
        };

        // Spreadsheet dan sheet Ringkasan
        $spreadsheet = new Spreadsheet();
        $summary = $spreadsheet->getActiveSheet();
        $summary->setTitle('Ringkasan');

        // Tabel ringkasan utama
        $summary->fromArray([
            ['Total Responden', $statistics['total_responden']],
            ['Rata-rata NRR', $statistics['avg_nrr']],
            ['IKM Total', $statistics['ikm_total']],
            ['Kategori Mutu', $statistics['kategori_mutu']],
        ], null, 'A1');
        $summary->getStyle('A1:A4')->getFont()->setBold(true);

        // Tabel per unsur
        $summary->fromArray([['No', 'Unsur Pelayanan', 'NRR', 'IKM', 'Kategori']], null, 'A6');
        $row = 7;
        $no = 1;
        foreach ($unsur_labels as $key => $label) {
            $nrr = $statistics['nrr_per_unsur'][$key];
            $ikm = $statistics['ikm_per_unsur'][$key];
            $summary->fromArray([$no++, $label, $nrr, $ikm, $kategoriMutu($ikm)], null, 'A' . $row);
            $row++;
        }
        $unsurStart = 7;
        $unsurEnd = $row - 1;

        // Style ringkasan + unsur
        $summary->getStyle('A6:E6')->getFont()->setBold(true);
        foreach (range('A', 'E') as $col) {
            $summary->getColumnDimension($col)->setAutoSize(true);
        }
        $summary->getStyle('A1:B4')->applyFromArray(['borders' => ['allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]]]);
        $summary->getStyle('A6:E' . $unsurEnd)->applyFromArray(['borders' => ['allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]]]);
        $summary->getStyle('A6:E6')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFEFEFEF');

        // Chart IKM per unsur
        $label = [new \PhpOffice\PhpSpreadsheet\Chart\DataSeriesValues('String', "'Ringkasan'!D6", null, 1)];
        $categories = [new \PhpOffice\PhpSpreadsheet\Chart\DataSeriesValues('String', "'Ringkasan'!B{$unsurStart}:B{$unsurEnd}", null, $unsurEnd - $unsurStart + 1)];
        $values = [new \PhpOffice\PhpSpreadsheet\Chart\DataSeriesValues('Number', "'Ringkasan'!D{$unsurStart}:D{$unsurEnd}", null, $unsurEnd - $unsurStart + 1)];
        $series = new \PhpOffice\PhpSpreadsheet\Chart\DataSeries(
            \PhpOffice\PhpSpreadsheet\Chart\DataSeries::TYPE_BARCHART,
            \PhpOffice\PhpSpreadsheet\Chart\DataSeries::GROUPING_CLUSTERED,
            range(0, count($values) - 1),
            $label,
            $categories,
            $values
        );
        $series->setPlotDirection(\PhpOffice\PhpSpreadsheet\Chart\DataSeries::DIRECTION_COL);
        $plotArea = new \PhpOffice\PhpSpreadsheet\Chart\PlotArea(null, [$series]);
        $title = new \PhpOffice\PhpSpreadsheet\Chart\Title('IKM per Unsur Pelayanan');
        $legend = new \PhpOffice\PhpSpreadsheet\Chart\Legend(\PhpOffice\PhpSpreadsheet\Chart\Legend::POSITION_RIGHT, null, false);
        $chart = new \PhpOffice\PhpSpreadsheet\Chart\Chart('chart1', $title, $legend, $plotArea);
        $chartTop = $unsurEnd + 2;
        $chartBottom = $unsurEnd + 18;
        $chart->setTopLeftPosition('A' . $chartTop);
        $chart->setBottomRightPosition('H' . $chartBottom);
        $summary->addChart($chart);

        // Interpretasi
        $interpRow = $chartBottom + 2;
        $summary->setCellValue('A' . $interpRow, 'Interpretasi Hasil IKM:');
        $summary->getStyle('A' . $interpRow)->getFont()->setBold(true);
        $summary->setCellValue('A' . ($interpRow + 1), 'IKM Total: ' . $statistics['ikm_total'] . ' | Kategori: ' . $statistics['kategori_mutu']);
        $summary->setCellValue('A' . ($interpRow + 2), 'Skala interpretasi: A (81.26–100), B (62.51–81.25), C (43.76–62.50), D (25.00–43.75).');

        // Sheet Data Responden
        $headers = array('ID', 'Tanggal', 'Jenis Kelamin', 'Pendidikan', 'Usia', 'Pekerjaan', 'Pekerjaan Lainnya', 'Jenis Layanan', 'Persyaratan', 'Prosedur', 'Waktu Pelayanan', 'Biaya/Tarif', 'Produk Layanan', 'Kompetensi Petugas', 'Perilaku Petugas', 'Sarana Prasarana', 'Penanganan Pengaduan');
        $dataSheet = $spreadsheet->createSheet();
        $dataSheet->setTitle('Data Responden');
        $dataSheet->fromArray($headers, null, 'A1');
        $r = 2;
        foreach ($surveys as $survey) {
            $dataSheet->fromArray([
                $survey->id,
                $survey->created_at,
                $survey->jenis_kelamin,
                $survey->pendidikan,
                $survey->usia,
                $survey->pekerjaan,
                $survey->pekerjaan_lainnya,
                $survey->jenis_layanan,
                $survey->u1,
                $survey->u2,
                $survey->u3,
                $survey->u4,
                $survey->u5,
                $survey->u6,
                $survey->u7,
                $survey->u8,
                $survey->u9
            ], null, 'A' . $r);
            $r++;
        }
        $lastCol = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($headers));
        $dataSheet->getStyle('A1:' . $lastCol . '1')->getFont()->setBold(true);
        $dataSheet->getStyle('A1:' . $lastCol . ($r - 1))->applyFromArray(['borders' => ['allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]]]);
        for ($i = 1; $i <= count($headers); $i++) {
            $dataSheet->getColumnDimensionByColumn($i)->setAutoSize(true);
        }

        // Output
        $filename = 'survey_kepuasan_masyarakat_' . date('Y-m-d_H-i-s') . '.xlsx';
        $writer = new Xlsx($spreadsheet);
        $writer->setIncludeCharts(true);
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        $writer->save('php://output');
        exit;
    }

    // Admin survey perilaku report
    public function admin_perilaku()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');

        $statistics = $this->perilaku_survey->get_survey_statistics(
            getCurrentIdUser(),
            $start_date,
            $end_date
        );

        $data = array();
        $data['title'] = 'Laporan Survey Perilaku';
        $data['content'] = 'survey/perilaku_index';
        $data['statistics'] = $statistics;
        $data['unsur_labels'] = $this->perilaku_survey->get_unsur_labels();
        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;

        return $this->load->view('master', $data);
    }

    // Export Excel (XLSX) - Survey Perilaku
    public function export_perilaku_csv()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');

        $this->db->where('desa_user_id', getCurrentIdUser());
        if ($start_date) {
            $this->db->where('created_at >=', $start_date . ' 00:00:00');
        }
        if ($end_date) {
            $this->db->where('created_at <=', $end_date . ' 23:59:59');
        }

        $surveys = $this->db->get('perilaku_survey')->result();
        if (empty($surveys)) {
            if (isset($this->session)) {
                $this->session->set_flashdata('error', 'Tidak ada data untuk diekspor');
            }
            return redirect('admin/survey-perilaku');
        }

        // Ringkasan statistik
        $statistics = $this->perilaku_survey->get_survey_statistics(getCurrentIdUser(), $start_date, $end_date);
        $unsur_labels = $this->perilaku_survey->get_unsur_labels();

        $kategoriMutu = function ($ikm) {
            if ($ikm >= 81.26) return 'A (Sangat Baik)';
            if ($ikm >= 62.51) return 'B (Baik)';
            if ($ikm >= 43.76) return 'C (Kurang Baik)';
            return 'D (Tidak Baik)';
        };

        $spreadsheet = new Spreadsheet();
        $summary = $spreadsheet->getActiveSheet();
        $summary->setTitle('Ringkasan');

        // Tabel ringkasan utama
        $summary->fromArray([
            ['Total Responden', $statistics['total_responden']],
            ['Rata-rata NRR', $statistics['avg_nrr']],
            ['IKM Total', $statistics['ikm_total']],
            ['Kategori Mutu', $statistics['kategori_mutu']],
        ], null, 'A1');
        $summary->getStyle('A1:A4')->getFont()->setBold(true);

        // Tabel per unsur perilaku
        $summary->fromArray([['No', 'Unsur', 'NRR', 'IKM', 'Kategori']], null, 'A6');
        $row = 7;
        $no = 1;
        foreach ($unsur_labels as $key => $label) {
            $nrr = $statistics['nrr_per_unsur'][$key];
            $ikm = $statistics['ikm_per_unsur'][$key];
            $summary->fromArray([$no++, $label, $nrr, $ikm, $kategoriMutu($ikm)], null, 'A' . $row);
            $row++;
        }
        $unsurStart = 7;
        $unsurEnd = $row - 1;

        // Style ringkasan + unsur
        $summary->getStyle('A6:E6')->getFont()->setBold(true);
        foreach (range('A', 'E') as $col) {
            $summary->getColumnDimension($col)->setAutoSize(true);
        }
        $summary->getStyle('A1:B4')->applyFromArray(['borders' => ['allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]]]);
        $summary->getStyle('A6:E' . $unsurEnd)->applyFromArray(['borders' => ['allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]]]);
        $summary->getStyle('A6:E6')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFEFEFEF');

        // Chart IKM per unsur
        $label = [new \PhpOffice\PhpSpreadsheet\Chart\DataSeriesValues('String', "'Ringkasan'!D6", null, 1)];
        $categories = [new \PhpOffice\PhpSpreadsheet\Chart\DataSeriesValues('String', "'Ringkasan'!B{$unsurStart}:B{$unsurEnd}", null, $unsurEnd - $unsurStart + 1)];
        $values = [new \PhpOffice\PhpSpreadsheet\Chart\DataSeriesValues('Number', "'Ringkasan'!D{$unsurStart}:D{$unsurEnd}", null, $unsurEnd - $unsurStart + 1)];
        $series = new \PhpOffice\PhpSpreadsheet\Chart\DataSeries(
            \PhpOffice\PhpSpreadsheet\Chart\DataSeries::TYPE_BARCHART,
            \PhpOffice\PhpSpreadsheet\Chart\DataSeries::GROUPING_CLUSTERED,
            range(0, count($values) - 1),
            $label,
            $categories,
            $values
        );
        $series->setPlotDirection(\PhpOffice\PhpSpreadsheet\Chart\DataSeries::DIRECTION_COL);
        $plotArea = new \PhpOffice\PhpSpreadsheet\Chart\PlotArea(null, [$series]);
        $title = new \PhpOffice\PhpSpreadsheet\Chart\Title('IKM per Unsur Perilaku');
        $legend = new \PhpOffice\PhpSpreadsheet\Chart\Legend(\PhpOffice\PhpSpreadsheet\Chart\Legend::POSITION_RIGHT, null, false);
        $chart = new \PhpOffice\PhpSpreadsheet\Chart\Chart('chart1', $title, $legend, $plotArea);
        $chartTop = $unsurEnd + 2;
        $chartBottom = $unsurEnd + 18;
        $chart->setTopLeftPosition('A' . $chartTop);
        $chart->setBottomRightPosition('H' . $chartBottom);
        $summary->addChart($chart);

        // Interpretasi
        $interpRow = $chartBottom + 2;
        $summary->setCellValue('A' . $interpRow, 'Interpretasi Hasil IKM:');
        $summary->getStyle('A' . $interpRow)->getFont()->setBold(true);
        $summary->setCellValue('A' . ($interpRow + 1), 'IKM Total: ' . $statistics['ikm_total'] . ' | Kategori: ' . $statistics['kategori_mutu']);
        $summary->setCellValue('A' . ($interpRow + 2), 'Skala interpretasi: A (81.26100), B (62.5181.25), C (43.7662.50), D (25.0043.75).');

        // Sheet Data Responden
        $headers = array('ID', 'Tanggal', 'Jenis Kelamin', 'Pendidikan', 'Usia', 'Pekerjaan', 'Pekerjaan Lainnya', 'Memberikan Uang/Barang/Makanan', 'Imbalan', 'Besaran Uang', 'Memberikan Sesuatu', 'Dipersulit', 'Jenis Pemberian', 'Hubungan Kekerabatan', 'Jasa Calo/Perantara', 'Diluar Jam Kerja/Kantor', 'Mendengar Terjadinya KKN');
        $dataSheet = $spreadsheet->createSheet();
        $dataSheet->setTitle('Data Responden');
        $dataSheet->fromArray($headers, null, 'A1');
        $r = 2;
        foreach ($surveys as $s) {
            $dataSheet->fromArray([
                $s->id,
                $s->created_at,
                $s->jenis_kelamin,
                $s->pendidikan,
                $s->usia,
                $s->pekerjaan,
                $s->pekerjaan_lainnya,
                $s->u1,
                $s->u2,
                $s->u3,
                $s->u4,
                $s->u5,
                $s->u6,
                $s->u7,
                $s->u8,
                $s->u9,
                $s->u10
            ], null, 'A' . $r);
            $r++;
        }
        $lastCol = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($headers));
        $dataSheet->getStyle('A1:' . $lastCol . '1')->getFont()->setBold(true);
        $dataSheet->getStyle('A1:' . $lastCol . ($r - 1))->applyFromArray(['borders' => ['allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]]]);
        for ($i = 1; $i <= count($headers); $i++) {
            $dataSheet->getColumnDimensionByColumn($i)->setAutoSize(true);
        }

        // Output
        $filename = 'survey_perilaku_' . date('Y-m-d_H-i-s') . '.xlsx';
        $writer = new Xlsx($spreadsheet);
        $writer->setIncludeCharts(true);
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        $writer->save('php://output');
        exit;
    }
}
