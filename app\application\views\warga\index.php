<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!-- ============================================================== -->
<!-- Container fluid  -->
<!-- ============================================================== -->
<div class="container-fluid">
	<div class="row">
		<div class="col-md-12">
			<div class="card">
				<div class="card-header">
					<div class="float-left">
						<h4 class="card-title">Data Warga <?= isset($setting->desa) ? $setting->desa : null ?></h4>
					</div>

					<div class="float-right d-flex">
						<div class="mr-1">
							<a href="<?= base_url(uri_string() . '/add') ?>" class="btn btn-primary btn-sm">
								Tambah
							</a>
						</div>

						<div class="dropdown mr-1">
							<button class="btn btn-success btn-sm dropdown-toggle" type="button" id="dropdownMenuButton1" data-toggle="dropdown" aria-expanded="false">
								Import
							</button>

							<ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
								<li>
									<a class="dropdown-item" href="javascript:;" onclick="importProdeskel()">Import dari Prodeskel</a>
								</li>

								<li>
									<a class="dropdown-item" href="javascript:;" onclick="importexcel()">Import Excel</a>
								</li>

								<li>
									<a class="dropdown-item" href="javascript:;" onclick="importbip()">Import BIP</a>
								</li>
							</ul>
						</div>

						<!-- <div class="mr-1">
							<button type="button" class="btn btn-danger btn-sm" onclick="invalidData()">Data tidak Valid</button>
						</div> -->

						<div class="mr-1">
							<a href="<?= base_url(uri_string() . '/export') ?>" class="btn btn-primary btn-sm">
								Export Excel
							</a>
						</div>

						<button type="button" class="btn btn-danger btn-sm" onclick="deleteAll()">
							Hapus Semua Data Warga
						</button>
					</div>
				</div>

				<div class="card-body">
					<form action="<?= base_url(uri_string()) ?>" method="GET" autocomplete="off">
						<div class="row">
							<div class="col-md-3">
								<div class="form-group">
									<label for="">Sumber Data</label>
									<select name="datasource" class="form-control">
										<option value="">- Semua -</option>
										<option value="Sistem" <?= $current_datasource == 'Sistem' ? 'selected' : null ?>>Sistem</option>
										<option value="Prodeskel" <?= $current_datasource == 'Prodeskel' ? 'selected' : null ?>>Prodeskel</option>
									</select>
								</div>
							</div>
							<div class="col-md-3">
								<div class="form-group">
									<label>Status</label>
									<select name="status" class="form-control">
										<option value="">- Semua -</option>
										<option value="Aktif" <?= $current_filterstatus == 'Aktif' ? 'selected' : null ?>>Aktif</option>
										<option value="Pindah" <?= $current_filterstatus == 'Pindah' ? 'selected' : null ?>>Pindah</option>
										<option value="Meninggal" <?= $current_filterstatus == 'Meninggal' ? 'selected' : null ?>>Meninggal</option>
									</select>
								</div>
							</div>
							<div class="col-md-3">
								<div class="form-group">
									<label>Desil</label>
									<select name="desil" class="form-control">
										<option value="">- Semua -</option>
										<?php for ($i = 1; $i <= 10; $i++) : ?>
											<option value="<?= $i ?>" <?= $current_filterdesil == $i ? 'selected' : null ?>><?= $i ?></option>
										<?php endfor; ?>
									</select>
								</div>
							</div>

							<div class="col-md-3">
								<div class="form-group">
									<label>Aksi</label>

									<div>
										<button type="submit" class="btn btn-primary">Filter</button>
									</div>
								</div>
							</div>
						</div>
					</form>

					<div class="table-responsive">
						<table id="datatablesWarga" class="table table-striped table-bordered no-wrap">
							<thead>
								<tr>
									<th>NIK</th>
									<th>Nama</th>
									<th>TTL</th>
									<th>Kelurahan</th>
									<th>Aksi</th>
								</tr>
							</thead>

							<tbody>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modal-default">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">Import Ecxel</h4>

				<button type="button" class="close" data-dismiss="modal" aria-label="Close" id="modal-close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>

			<form id="frmimport" action="<?= base_url(uri_string() . '/import') ?>" method="POST" autocomplete="off" enctype="multipart/form-data">
				<input type="hidden" name="id">
				<div class="modal-body">
					<div class="form-group">
						<label for="">File Excel</label>

						<input type="file" class="form-control" name="file" accept=".xls, .xlsx" required>

						<div class="float-left">
							<small class="d-block text-danger mt-1">File yang harus diupload : .xls, xlsx</small>
						</div>

						<div class="float-right">
							<a href="<?= base_url(uri_string() . '/format/excel') ?>" target="_blank"><small>[Download Format Excel]</small></a>
						</div>

						<div class="clearfix"></div>
					</div>
				</div>

				<div class="modal-footer">
					<button type="button" class="btn btn-danger" data-dismiss="modal" id="btn-close-bank">Close</button>
					<button type="submit" class="btn btn-primary">Import</button>
				</div>
			</form>
		</div>
	</div>
</div>
<!-- ============================================================== -->
<!-- End Container fluid  -->
<!-- ============================================================== -->

<script>
	window.onload = function() {
		$('#datatablesWarga').DataTable({
			ordering: false,
			processing: true,
			serverSide: true,
			ajax: {
				url: '<?= base_url(uri_string() . '/datatables') ?>',
				type: 'POST',
				data: {
					status: '<?= $current_filterstatus ?>',
					datasource: '<?= $current_datasource ?>',
					desil: '<?= $current_filterdesil ?>'
				}
			},
		});

		$.AjaxRequest('#frmimport', {
			success: function(response) {
				if (response.RESULT == 'OK') {
					return swalMessageSuccess(response.MESSAGE, ok => {
						return window.location.reload();
					})
				} else {
					return swalMessageFailed(response.MESSAGE);
				}
			},
			error: function() {
				return swalError();
			}
		});
	};

	function deleteAll() {
		return swal({
			title: 'Apakah anda yakin?',
			text: 'Anda tidak dapat mengembalikan data yang telah dihapus!',
			icon: 'warning',
			buttons: true,
			dangerMode: true
		}).then(ok => {
			if (ok) {
				$.ajax({
					url: '<?= base_url(uri_string() . '/delete/all') ?>',
					method: 'POST',
					dataType: 'json',
					success: function(response) {
						if (response.RESULT == 'OK') {
							return swalMessageSuccess(response.MESSAGE, ok => {
								window.location.reload();
							});
						} else {
							return swalMessageFailed(response.MESSAGE);
						}
					}
				}).fail(function() {
					return swalError();
				});
			}
		});
	}

	function deleteWarga(id) {
		return swal({
			title: 'Apakah anda yakin?',
			text: 'Apakah anda yakin ingin menghapus data ini?',
			icon: 'warning',
			buttons: true,
			dangerMode: true
		}).then(ok => {
			if (ok) {
				$.ajax({
					url: '<?= base_url(uri_string() . '/delete') ?>',
					method: 'POST',
					dataType: 'json',
					data: {
						id: id
					},
					success: function(response) {
						if (response.RESULT == 'OK') {
							return swalMessageSuccess(response.MESSAGE, ok => {
								window.location.reload();
							});
						} else {
							return swalMessageFailed(response.MESSAGE);
						}
					}
				}).fail(function() {
					return swalError();
				});
			}
		});
	}

	function detail(id) {
		$.ajax({
			url: '<?= base_url(uri_string() . '/detail') ?>',
			method: 'POST',
			dataType: 'json',
			data: {
				id: id
			},
			success: function(response) {
				if (response.RESULT == 'OK') {
					$('#modalGlobal').html(response.CONTENT);
					$('#modalGlobal').modal('show');
				} else {
					return swalMessageFailed(response.MESSAGE);
				}
			}
		}).fail(function() {
			return swalError();
		});
	}

	function importexcel() {
		$('#modal-default').modal('show');
	}

	function importbip() {
		$.ajax({
			url: '<?= base_url(uri_string() . '/import/bip') ?>',
			method: 'POST',
			dataType: 'json',
			success: function(response) {
				if (response.RESULT == 'OK') {
					$('#modalGlobal').html(response.CONTENT);
					$('#modalGlobal').modal('show');
				} else {
					return swalMessageFailed(response.MESSAGE);
				}
			}
		}).fail(function() {
			return swalError();
		});
	}

	function scan_rfid(nik) {
		$.ajax({
			url: '<?= base_url(uri_string() . '/rfid') ?>',
			method: 'POST',
			dataType: 'json',
			data: {
				nik: nik
			},
			success: function(response) {
				if (response.RESULT == 'OK') {
					$('#modalGlobal').html(response.CONTENT);
					$('#modalGlobal').modal('show');
				} else {
					return swalMessageFailed(response.MESSAGE);
				}
			}
		}).fail(function() {
			return swalError();
		});
	}

	function importProdeskel() {
		$.ajax({
			url: '<?= base_url(uri_string() . '/import/prodeskel') ?>',
			method: 'POST',
			dataType: 'json',
			success: function(response) {
				if (response.RESULT == 'OK') {
					$('#modalGlobal').html(response.CONTENT);
					$('#modalGlobal').modal('show');
				} else {
					return swalMessageFailed(response.MESSAGE);
				}
			}
		}).fail(function() {
			return swalError();
		})
	}

	function invalidData() {
		$.ajax({
			url: '<?= base_url(uri_string() . '/datainvalid') ?>',
			method: 'POST',
			dataType: 'json',
			success: function(response) {
				if (response.RESULT == 'OK') {
					$('#modalGlobal').html(response.CONTENT);
					$('#modalGlobal').modal('show');
				} else {
					return swalMessageFailed(response.MESSAGE);
				}
			}
		}).fail(function() {
			return swalError();
		});
	}
</script>