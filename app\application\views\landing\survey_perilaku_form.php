<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>

<div class="container" style="margin-top: 100px; padding-top: 50px;">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card" style="box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <div class="card-header" style="background-color: #007bff; color: white;">
                    <div class="text-center">
                        <h3 class="mb-0">SURVEY PERILAKU</h3>
                        <p class="mb-0">PADA UNIT PELAYANAN PUBLIK <?= strtoupper($setting->desa ?? 'DESA') ?></p>
                    </div>
                </div>

                <div class="card-body">
                    <form id="surveyPerilakuForm" method="POST" action="<?= base_url('survey/perilaku/submit') ?>">
                        <!-- PROFIL RESPONDEN -->
                        <div class="mb-4">
                            <h5 style="color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 8px;">PROFIL RESPONDEN</h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Jenis Kelamin <span class="text-danger">*</span></label>
                                    <div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="jenis_kelamin" id="jk_l" value="L" required>
                                            <label class="form-check-label" for="jk_l">Laki-laki</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="jenis_kelamin" id="jk_p" value="P" required>
                                            <label class="form-check-label" for="jk_p">Perempuan</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="pendidikan" class="form-label">Pendidikan <span class="text-danger">*</span></label>
                                    <select class="form-control" id="pendidikan" name="pendidikan" required>
                                        <option value="">Pilih Pendidikan</option>
                                        <option value="SD">SD</option>
                                        <option value="SMP">SMP</option>
                                        <option value="SMA">SMA</option>
                                        <option value="S1">S1</option>
                                        <option value="S2">S2</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="usia" class="form-label">Usia <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="usia" name="usia" min="17" max="100" required>
                                        <span class="input-group-text">tahun</span>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="pekerjaan" class="form-label">Pekerjaan <span class="text-danger">*</span></label>
                                    <select class="form-control" id="pekerjaan" name="pekerjaan" required>
                                        <option value="">Pilih Pekerjaan</option>
                                        <option value="PNS">PNS</option>
                                        <option value="TNI">TNI</option>
                                        <option value="POLRI">POLRI</option>
                                        <option value="SWASTA">Swasta</option>
                                        <option value="WIRAUSAHA">Wirausaha</option>
                                        <option value="LAINNYA">Lainnya</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3" id="pekerjaan_lainnya_group" style="display: none;">
                                    <label for="pekerjaan_lainnya" class="form-label">Sebutkan Pekerjaan Lainnya</label>
                                    <input type="text" class="form-control" id="pekerjaan_lainnya" name="pekerjaan_lainnya">
                                </div>
                            </div>
                        </div>

                        <!-- PERTANYAAN PERILAKU -->
                        <div class="mb-4">
                            <h5 style="color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 8px;">PERTANYAAN PERILAKU</h5>
                            <p class="text-muted small">Pilih jawaban sesuai dengan pengalaman Anda</p>

                            <?php
                            $questions = array(
                                'u1' => array(
                                    'title' => 'Apakah Saudara/i pernah memberikan uang/barang/makanan kepada perangkat Desa?',
                                    'options' => array('Sering', 'Kadang-kadang', 'Pernah', 'Tidak Pernah')
                                ),
                                'u2' => array(
                                    'title' => 'Apakah Saudara/i pernah dihubungi dan ditawari oleh Perangkat Desa yang akan membantu dalam pengurusan surat/berkas dengan menerima imbalan tertentu?',
                                    'options' => array('Sering', 'Kadang-kadang', 'Pernah', 'Tidak Pernah')
                                ),
                                'u3' => array(
                                    'title' => 'Berapa besarnya jumlah uang yang diminta/diberikan kepada petugas pelayanan desa terkait administrasi pelayanan desa?',
                                    'options' => array('> Rp 100.000', 'Rp 51.000 - Rp 100.000', 'Rp 10.000 - Rp 50.000', 'Gratis')
                                ),
                                'u4' => array(
                                    'title' => 'Apakah saudara pernah memberikan sesuatu kepada petugas pelayanan desa?',
                                    'options' => array('Sering', 'Kadang-kadang', 'Pernah', 'Tidak Pernah')
                                ),
                                'u5' => array(
                                    'title' => 'Apakah saudara/i pernah dipersulit terkait pelayanan di desa?',
                                    'options' => array('Sering', 'Kadang-kadang', 'Pernah', 'Tidak Pernah')
                                ),
                                'u6' => array(
                                    'title' => 'Jenis pemberian apakah yang diberikan oleh saudara/i kepada petugas pelayanan desa?',
                                    'options' => array('Uang', 'Barang', 'Makanan', 'Tidak Pernah')
                                ),
                                'u7' => array(
                                    'title' => 'Apakah saudara/i mengetahui hubungan kekerabatan antar aparatur desa mempengaruhi keputusan dalam pengadaan/recruitment perangkat Desa?',
                                    'options' => array('Sangat Berpengaruh', 'Cukup Berpengaruh', 'Tidak Berpengaruh', 'Sangat Tidak Berpengaruh')
                                ),
                                'u8' => array(
                                    'title' => 'Apakah saudara/i pernah menggunakan jasa (perantara/calo) dalam pengurusan surat/dokumen di desa?',
                                    'options' => array('Sering', 'Kadang-kadang', 'Pernah', 'Tidak Pernah')
                                ),
                                'u9' => array(
                                    'title' => 'Apakah saudara/i pernah mengurus surat/dokumen tertentu melalui perangkat desa di luar kantor?',
                                    'options' => array('Sering', 'Kadang-kadang', 'Pernah', 'Tidak Pernah')
                                ),
                                'u10' => array(
                                    'title' => 'Apakah saudara/i pernah melihat dan/atau mendengar terjadinya praktik KKN (Korupsi, Kolusi, Nepotisme) di desa?',
                                    'options' => array('Sering', 'Kadang-kadang', 'Pernah', 'Tidak Pernah')
                                )
                            );

                            $counter = 1;
                            foreach ($questions as $key => $question): ?>
                                <div class="mb-4 p-3 border rounded">
                                    <h6 class="fw-bold"><?= $counter ?>. <?= $question['title'] ?></h6>
                                    <div class="row">
                                        <?php foreach ($question['options'] as $index => $option): ?>
                                            <div class="col-md-6 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="<?= $key ?>" id="<?= $key ?>_<?= $index + 1 ?>" value="<?= $index + 1 ?>" required>
                                                    <label class="form-check-label" for="<?= $key ?>_<?= $index + 1 ?>">
                                                        <?= chr(97 + $index) ?>. <?= $option ?>
                                                    </label>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php
                                $counter++;
                            endforeach; ?>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg px-5">
                                <i class="fas fa-paper-plane me-2"></i>Kirim Survey
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const pekerjaanSelect = document.getElementById('pekerjaan');
        const pekerjaanLainnyaGroup = document.getElementById('pekerjaan_lainnya_group');
        const pekerjaanLainnyaInput = document.getElementById('pekerjaan_lainnya');

        pekerjaanSelect.addEventListener('change', function() {
            if (this.value === 'LAINNYA') {
                pekerjaanLainnyaGroup.style.display = 'block';
                pekerjaanLainnyaInput.required = true;
            } else {
                pekerjaanLainnyaGroup.style.display = 'none';
                pekerjaanLainnyaInput.required = false;
                pekerjaanLainnyaInput.value = '';
            }
        });

        $('#surveyPerilakuForm').on('submit', function(e) {
            e.preventDefault();

            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                beforeSend: function() {
                    $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Mengirim...');
                },
                success: function(response) {
                    if (response.RESULT === 'OK') {
                        swal('Berhasil!', response.MESSAGE, 'success').then(function() {
                            window.location.href = '<?= base_url() ?>';
                        });
                    } else {
                        swal('Gagal!', response.MESSAGE, 'error');
                    }
                },
                error: function() {
                    swal('Error!', 'Terjadi kesalahan sistem. Silakan coba lagi.', 'error');
                },
                complete: function() {
                    $('button[type="submit"]').prop('disabled', false).html('<i class="fas fa-paper-plane me-2"></i>Kirim Survey');
                }
            });
        });
    });
</script>

